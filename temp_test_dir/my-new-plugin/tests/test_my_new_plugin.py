import pytest
from plugginger.api import plugin, service, PluginBase, PluggingerAppBuilder
from plugginger.api.app import PluggingerAppInstance

@plugin(name="my_new_plugin", version="0.1.0")
class MyNewPluginPlugin(PluginBase):
    @service()
    async def hello(self) -> str:
        return "Hello from my-new-plugin!"

@pytest.mark.asyncio
async def test_my_new_plugin_hello_service():
    builder = PluggingerAppBuilder(app_name="test_app")
    builder.include(MyNewPluginPlugin)
    app = builder.build()
    
    # Corrected: Use call_service and await it
    result = await app.call_service("my_new_plugin.hello")
    assert result == "Hello from my-new-plugin!"