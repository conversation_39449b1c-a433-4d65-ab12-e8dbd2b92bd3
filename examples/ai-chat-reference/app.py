"""AI-Chat Reference App - Main Application Builder."""

from __future__ import annotations

import asyncio
import logging
import os
from typing import Any

from plugginger.api.app import PluggingerAppInstance
from plugginger.api.builder import PluggingerAppBuilder
from plugginger.config.models import GlobalAppConfig

# Import plugins
from plugins.chat_ai import ChatAIPlugin
from plugins.memory_store import MemoryStorePlugin
from plugins.web_api import WebAPIPlugin


def create_app(
    app_config: dict[str, Any] | None = None,
    enable_manifests: bool = True
) -> PluggingerAppInstance:
    """
    Create and configure the AI-Chat Reference App.

    This is the main factory function for creating the Plugginger application
    with all required plugins configured and ready to run.

    Args:
        app_config: Optional application configuration
        enable_manifests: Whether to enable manifest loading and validation

    Returns:
        Configured PluggingerAppInstance ready to start
    """
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # Create builder
    builder = PluggingerAppBuilder(app_name="ai_chat_reference")

    # Enable manifest loading if requested (disabled by default due to schema mismatch)
    if enable_manifests:
        builder.enable_manifest_loading(require_manifests=False)

    # Include plugins in dependency order
    # 1. Memory Store (no dependencies)
    builder.include(MemoryStorePlugin)

    # 2. Chat AI (depends on memory_store)
    builder.include(ChatAIPlugin)

    # 3. Web API (depends on chat_ai and memory_store)
    builder.include(WebAPIPlugin)

    # Build the application
    global_config = GlobalAppConfig(
        max_fractal_depth=5,
        default_event_listener_timeout_seconds=30.0,
        **(app_config or {})
    )

    app = builder.build(global_config)

    return app


async def start_app(
    app: PluggingerAppInstance,
    start_web_server: bool = True,
    server_host: str = "0.0.0.0",
    server_port: int = 8000
) -> None:
    """
    Start the AI-Chat Reference App with all plugins.

    Args:
        app: The PluggingerAppInstance to start
        start_web_server: Whether to start the web server
        server_host: Host for the web server
        server_port: Port for the web server
    """
    try:
        # Start all plugins
        await app.start_all_plugins()

        # Start web server if requested
        if start_web_server:
            await app.call_service(
                "web_api.start_server",
                host=server_host,
                port=server_port
            )

            print("🚀 AI-Chat Reference App started!")
            print(f"📡 Web API available at: http://{server_host}:{server_port}")
            print(f"🏥 Health check: http://{server_host}:{server_port}/health")
            print(f"💬 Chat endpoint: http://{server_host}:{server_port}/chat")
            print(f"📋 API docs: http://{server_host}:{server_port}/docs")

        # Log application status
        health_status = await app.call_service("web_api.get_health_status")
        app.logger.info(f"Application health: {health_status['status']}")

        # Log available services
        services = app.list_services()
        app.logger.info(f"Available services: {len(services)}")
        for service in services:
            app.logger.debug(f"  - {service}")

    except Exception as e:
        app.logger.error(f"Failed to start application: {e}")
        raise


async def stop_app(app: PluggingerAppInstance) -> None:
    """
    Stop the AI-Chat Reference App gracefully.

    Args:
        app: The PluggingerAppInstance to stop
    """
    try:
        # Stop web server
        await app.call_service("web_api.stop_server")

        # Stop all plugins
        await app.stop_all_plugins()

        print("🛑 AI-Chat Reference App stopped gracefully")

    except Exception as e:
        app.logger.error(f"Error during app shutdown: {e}")


async def main() -> None:
    """
    Main entry point for running the AI-Chat Reference App.

    This function demonstrates how to create, start, and run the application.
    It's designed to be used for development and testing.
    """
    # Get configuration from environment
    server_host = os.getenv("SERVER_HOST", "0.0.0.0")
    server_port = int(os.getenv("SERVER_PORT", "8000"))
    enable_manifests = os.getenv("ENABLE_MANIFESTS", "false").lower() == "true"

    # Create application
    print("🔧 Creating AI-Chat Reference App...")
    app = create_app(enable_manifests=enable_manifests)

    try:
        # Start application
        await start_app(
            app,
            start_web_server=True,
            server_host=server_host,
            server_port=server_port
        )

        # Keep running until interrupted
        print("✨ Application running. Press Ctrl+C to stop.")
        try:
            while True:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            print("\n🔄 Shutting down...")

    finally:
        # Clean shutdown
        await stop_app(app)


if __name__ == "__main__":
    # Run the application
    asyncio.run(main())


# Factory function for plugginger inspect command
def create_app_for_inspection() -> PluggingerAppBuilder:
    """
    Factory function specifically for the `plugginger inspect` command.

    This function creates the app builder without manifest loading for now since
    the YAML manifests need to be converted to the internal format.
    It's the entry point that the inspect command will use.

    Returns:
        PluggingerAppBuilder configured for inspection
    """
    # Create builder
    builder = PluggingerAppBuilder(app_name="ai_chat_reference")

    # Include plugins in dependency order
    # 1. Memory Store (no dependencies)
    builder.include(MemoryStorePlugin)

    # 2. Chat AI (depends on memory_store)
    builder.include(ChatAIPlugin)

    # 3. Web API (depends on chat_ai and memory_store)
    builder.include(WebAPIPlugin)

    return builder


# Export the factory function for external use
__all__ = ["create_app", "start_app", "stop_app", "create_app_for_inspection"]
