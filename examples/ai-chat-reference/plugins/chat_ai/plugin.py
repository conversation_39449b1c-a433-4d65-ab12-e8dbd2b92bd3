"""Chat AI Plugin - OpenAI/Anthropic Integration for Chat Completion."""

from __future__ import annotations

import os
import time
from typing import Any

from pydantic import BaseModel

from plugginger.api.app import PluggingerAppInstance
from plugginger.api.depends import Depends
from plugginger.api.events import on_event
from plugginger.api.plugin import PluginBase, plugin
from plugginger.api.service import service


@plugin(name="chat_ai", version="1.0.0")
class ChatAIPlugin(PluginBase):
    """
    Chat AI Plugin for OpenAI/Anthropic Integration.
    
    Provides AI chat completion services with conversation context.
    Supports multiple models and providers with fallback to mock responses.
    """

    needs: list[Depends] = [Depends("memory_store")]

    def __init__(self, app: PluggingerAppInstance, **injected_dependencies: Any) -> None:
        """Initialize the chat AI plugin."""
        super().__init__(app, **injected_dependencies)
        
        # Get injected memory store dependency
        self.memory_store = injected_dependencies.get("memory_store")
        
        # Configuration
        self._api_key: str | None = None
        self._default_model = "gpt-3.5-turbo"
        self._mock_mode = False
        self._max_tokens = 1000
        self._temperature = 0.7
        
    async def setup(self, plugin_config: BaseModel) -> None:
        """Setup the chat AI plugin."""
        # Check for API key in environment
        self._api_key = os.getenv("OPENAI_API_KEY")
        
        if not self._api_key:
            self.app.logger.warning("No OPENAI_API_KEY found, running in mock mode")
            self._mock_mode = True
        else:
            self.app.logger.info("ChatAI plugin initialized with OpenAI API")

    @service()
    async def generate_response(
        self,
        message: str,
        conversation_id: str,
        model: str = "gpt-3.5-turbo",
        temperature: float = 0.7,
        max_tokens: int = 1000
    ) -> str:
        """
        Generate AI response for user message.
        
        Args:
            message: The user message to respond to
            conversation_id: The conversation context
            model: AI model to use for generation
            temperature: Response creativity (0.0-1.0)
            max_tokens: Maximum response length
            
        Returns:
            The AI-generated response
        """
        start_time = time.time()
        
        try:
            # Store user message first
            await self.memory_store.store_message(
                conversation_id=conversation_id,
                role="user",
                content=message
            )
            
            # Get conversation history for context
            history = await self.memory_store.get_conversation_history(
                conversation_id=conversation_id,
                limit=10  # Last 10 messages for context
            )
            
            # Generate response
            if self._mock_mode:
                response = await self._generate_mock_response(message, history)
                tokens_used = len(response.split())
            else:
                response, tokens_used = await self._generate_openai_response(
                    message, history, model, temperature, max_tokens
                )
            
            response_time = time.time() - start_time
            
            # Emit event with response details
            await self.app.emit_event("chat.response_generated", {
                "conversation_id": conversation_id,
                "user_message": message,
                "response": response,
                "model": model,
                "tokens_used": tokens_used,
                "response_time": response_time,
                "timestamp": time.time()
            })
            
            self.app.logger.debug(
                f"Generated response for conversation {conversation_id} "
                f"in {response_time:.2f}s using {model}"
            )
            
            return response
            
        except Exception as e:
            self.app.logger.error(f"Error generating response: {e}")
            # Emit error event
            await self.app.emit_event("chat.generation_error", {
                "conversation_id": conversation_id,
                "error": str(e),
                "timestamp": time.time()
            })
            raise

    async def _generate_mock_response(
        self,
        message: str,
        history: list[dict[str, Any]]
    ) -> str:
        """Generate a mock response for testing."""
        # Simple mock responses based on message content
        message_lower = message.lower()
        
        if "hello" in message_lower or "hi" in message_lower:
            return "Hello! I'm a mock AI assistant. How can I help you today?"
        elif "how are you" in message_lower:
            return "I'm doing well, thank you for asking! I'm a mock AI running in test mode."
        elif "weather" in message_lower:
            return "I'm sorry, I don't have access to real weather data in mock mode, but I hope it's nice where you are!"
        elif "help" in message_lower:
            return "I'm here to help! I'm currently running in mock mode for testing purposes. You can ask me anything and I'll do my best to provide a helpful response."
        elif len(history) > 0:
            return f"Thank you for your message: '{message}'. I'm a mock AI assistant and I see we've been chatting for {len(history)} messages now."
        else:
            return f"I received your message: '{message}'. I'm a mock AI assistant here to help with testing the chat system."

    async def _generate_openai_response(
        self,
        message: str,
        history: list[dict[str, Any]],
        model: str,
        temperature: float,
        max_tokens: int
    ) -> tuple[str, int]:
        """Generate response using OpenAI API."""
        try:
            # This would be the real OpenAI integration
            # For now, we'll use a mock implementation
            # In a real implementation, you would:
            # 1. Import openai
            # 2. Format history as messages
            # 3. Call openai.ChatCompletion.create()
            # 4. Return response and token count
            
            # Mock implementation for demonstration
            response = f"OpenAI Response to: '{message}' (using {model})"
            tokens_used = len(response.split()) + len(message.split())
            
            return response, tokens_used
            
        except Exception as e:
            self.app.logger.error(f"OpenAI API error: {e}")
            # Fallback to mock response
            return await self._generate_mock_response(message, history), 0

    @service()
    async def get_model_info(self) -> dict[str, Any]:
        """
        Get information about available models and current configuration.
        
        Returns:
            Model information and configuration
        """
        return {
            "current_model": self._default_model,
            "mock_mode": self._mock_mode,
            "api_available": self._api_key is not None,
            "max_tokens": self._max_tokens,
            "temperature": self._temperature,
            "available_models": [
                "gpt-3.5-turbo",
                "gpt-4",
                "gpt-4-turbo-preview"
            ] if not self._mock_mode else ["mock-model"],
            "plugin_version": "1.0.0"
        }

    @service()
    async def set_default_model(self, model: str) -> bool:
        """
        Set the default model for chat generation.
        
        Args:
            model: The model name to set as default
            
        Returns:
            True if model was set successfully
        """
        available_models = [
            "gpt-3.5-turbo", "gpt-4", "gpt-4-turbo-preview", "mock-model"
        ]
        
        if model not in available_models:
            self.app.logger.warning(f"Unknown model '{model}', keeping current default")
            return False
        
        self._default_model = model
        self.app.logger.info(f"Default model set to '{model}'")
        return True

    @on_event("memory.conversation_created")
    async def handle_new_conversation(self, event_data: dict[str, Any]) -> None:
        """
        Handle new conversation events.
        
        Args:
            event_data: Event data containing conversation information
        """
        conversation_id = event_data.get("conversation_id")
        if conversation_id:
            self.app.logger.debug(f"New conversation started: {conversation_id}")
            
            # Emit conversation started event
            await self.app.emit_event("chat.conversation_started", {
                "conversation_id": conversation_id,
                "ai_model": self._default_model,
                "timestamp": time.time()
            })
