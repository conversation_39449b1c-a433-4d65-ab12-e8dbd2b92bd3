"""
Integration tests for PluggingerAppBuilder discovery functionality.

Tests the integration between PluggingerAppBuilder and the plugin discovery
system, ensuring that discovered plugins are properly included and can be
used in applications.
"""

from __future__ import annotations

import tempfile
from pathlib import Path

import pytest

from plugginger.api.builder import PluggingerApp<PERSON>uilder
from plugginger.discovery.models import DiscoveryResult


class TestBuilderDiscoveryIntegration:
    """Integration test cases for builder discovery functionality."""

    def test_discover_and_include_plugins_basic(self) -> None:
        """Test basic plugin discovery and inclusion via builder."""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create a test plugin
            plugin_dir = Path(temp_dir) / "test_plugin"
            plugin_dir.mkdir()

            # Create manifest
            manifest_content = """
manifest_version: "1.0.0"
metadata:
  name: "test_service"
  version: "1.0.0"
  description: "Test service plugin"
  author: "Test Author"
  license: "MIT"
  keywords: ["test", "service"]

runtime:
  plugginger_version: ">=0.9.0"
  execution_mode: "thread"

services:
  - name: "greet"
    description: "Greet someone"
    parameters:
      - name: "name"
        type: "str"
        required: true
    returns:
      type: "str"

event_listeners: []
dependencies: []
"""
            (plugin_dir / "manifest.yaml").write_text(manifest_content)

            # Create plugin class
            plugin_content = '''
from plugginger.api.plugin import PluginBase, plugin
from plugginger.api.service import service

@plugin(name="test_service", version="1.0.0")
class TestServicePlugin(PluginBase):
    """Test service plugin for discovery integration."""

    @service()
    async def greet(self, name: str) -> str:
        """Greet someone by name."""
        return f"Hello, {name}!"
'''
            (plugin_dir / "plugin.py").write_text(plugin_content)

            # Test discovery and inclusion
            builder = PluggingerAppBuilder("test_app")
            result = builder.discover_and_include_plugins(temp_dir)

            # Verify discovery result
            assert isinstance(result, DiscoveryResult)
            assert len(result.discovered_plugins) == 1
            assert result.successful_discoveries == 1
            assert not result.has_errors

            discovered_plugin = result.discovered_plugins[0]
            assert discovered_plugin.name == "test_service"
            assert discovered_plugin.plugin_class.__name__ == "TestServicePlugin"

            # Build and test the app
            app = builder.build()
            assert app is not None

            # Verify plugin was included
            registered_plugins = app._registered_plugin_classes
            assert "test_service" in registered_plugins

            # Verify services are available
            services = app.list_services()
            assert "test_service.greet" in services

    @pytest.mark.asyncio
    async def test_discover_and_include_plugins_functional(self) -> None:
        """Test that discovered plugins are functionally working."""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create a functional test plugin
            plugin_dir = Path(temp_dir) / "calculator"
            plugin_dir.mkdir()

            # Create manifest
            manifest_content = """
manifest_version: "1.0.0"
metadata:
  name: "calculator"
  version: "1.0.0"
  description: "Simple calculator plugin"

runtime:
  plugginger_version: ">=0.9.0"

services:
  - name: "add"
    parameters:
      - name: "a"
        type: "int"
        required: true
      - name: "b"
        type: "int"
        required: true
    returns:
      type: "int"

event_listeners: []
dependencies: []
"""
            (plugin_dir / "manifest.yaml").write_text(manifest_content)

            # Create plugin class
            plugin_content = '''
from plugginger.api.plugin import PluginBase, plugin
from plugginger.api.service import service

@plugin(name="calculator", version="1.0.0")
class CalculatorPlugin(PluginBase):
    """Simple calculator plugin."""

    @service()
    async def add(self, a: int, b: int) -> int:
        """Add two numbers."""
        return a + b
'''
            (plugin_dir / "plugin.py").write_text(plugin_content)

            # Build app with discovered plugin
            builder = PluggingerAppBuilder("calc_app")
            builder.discover_and_include_plugins(temp_dir)
            app = builder.build()

            # Start the app and test functionality
            await app.start_all_plugins()

            try:
                # Test the service
                result_value = await app.call_service("calculator.add", a=5, b=3)
                assert result_value == 8

            finally:
                await app.stop_all_plugins()

    def test_discover_plugins_without_including(self) -> None:
        """Test discovering plugins without automatically including them."""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create multiple test plugins
            for i in range(3):
                plugin_dir = Path(temp_dir) / f"plugin_{i}"
                plugin_dir.mkdir()

                # Create manifest
                manifest_content = f"""
manifest_version: "1.0.0"
metadata:
  name: "plugin_{i}"
  version: "1.{i}.0"
runtime:
  plugginger_version: ">=0.9.0"
services: []
event_listeners: []
dependencies: []
"""
                (plugin_dir / "manifest.yaml").write_text(manifest_content)

                # Create plugin class
                plugin_content = f'''
from plugginger.api.plugin import PluginBase, plugin

@plugin(name="plugin_{i}", version="1.{i}.0")
class Plugin{i}(PluginBase):
    """Test plugin {i}."""
    pass
'''
                (plugin_dir / "plugin.py").write_text(plugin_content)

            # Test discovery without inclusion
            builder = PluggingerAppBuilder("test_app")
            result = builder.discover_plugins(temp_dir)

            # Verify discovery found all plugins
            assert len(result.discovered_plugins) == 3
            assert result.successful_discoveries == 3

            # Verify no plugins were included yet
            registered_plugins = builder.get_registered_plugin_classes()
            assert len(registered_plugins) == 0

            # Conditionally include plugins (e.g., only version 1.1.0 and above)
            for plugin in result.discovered_plugins:
                version = plugin.manifest.metadata.version
                if version >= "1.1.0":
                    builder.include(plugin.plugin_class)

            # Verify only selected plugins were included
            registered_plugins = builder.get_registered_plugin_classes()
            assert len(registered_plugins) == 2  # plugin_1 and plugin_2
            assert "plugin_1" in registered_plugins
            assert "plugin_2" in registered_plugins
            assert "plugin_0" not in registered_plugins

    def test_discover_and_include_with_manifest_loading_disabled(self) -> None:
        """Test discovery with manifest loading initially disabled."""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create a test plugin
            plugin_dir = Path(temp_dir) / "test_plugin"
            plugin_dir.mkdir()

            manifest_content = """
manifest_version: "1.0.0"
metadata:
  name: "manifest_test"
  version: "1.0.0"
runtime:
  plugginger_version: ">=0.9.0"
services: []
event_listeners: []
dependencies: []
"""
            (plugin_dir / "manifest.yaml").write_text(manifest_content)

            plugin_content = '''
from plugginger.api.plugin import PluginBase, plugin

@plugin(name="manifest_test", version="1.0.0")
class ManifestTestPlugin(PluginBase):
    pass
'''
            (plugin_dir / "plugin.py").write_text(plugin_content)

            # Create builder with manifest loading disabled
            builder = PluggingerAppBuilder("test_app")
            builder.disable_manifest_loading()

            # Discovery should automatically enable manifest loading
            result = builder.discover_and_include_plugins(temp_dir, enable_manifests=True)

            assert len(result.discovered_plugins) == 1
            assert result.successful_discoveries == 1

            # Verify manifest loading was enabled
            assert builder._manifest_loading_enabled

    def test_discover_and_include_with_errors(self) -> None:
        """Test discovery behavior when some plugins have errors."""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create a valid plugin
            valid_plugin_dir = Path(temp_dir) / "valid_plugin"
            valid_plugin_dir.mkdir()

            manifest_content = """
manifest_version: "1.0.0"
metadata:
  name: "valid_plugin"
  version: "1.0.0"
runtime:
  plugginger_version: ">=0.9.0"
services: []
event_listeners: []
dependencies: []
"""
            (valid_plugin_dir / "manifest.yaml").write_text(manifest_content)

            plugin_content = '''
from plugginger.api.plugin import PluginBase, plugin

@plugin(name="valid_plugin", version="1.0.0")
class ValidPlugin(PluginBase):
    pass
'''
            (valid_plugin_dir / "plugin.py").write_text(plugin_content)

            # Create an invalid plugin (manifest only, no plugin file)
            invalid_plugin_dir = Path(temp_dir) / "invalid_plugin"
            invalid_plugin_dir.mkdir()

            invalid_manifest_content = """
manifest_version: "1.0.0"
metadata:
  name: "invalid_plugin"
  version: "1.0.0"
runtime:
  plugginger_version: ">=0.9.0"
services: []
event_listeners: []
dependencies: []
"""
            (invalid_plugin_dir / "manifest.yaml").write_text(invalid_manifest_content)
            # Note: No plugin.py file created

            # Test discovery
            builder = PluggingerAppBuilder("test_app")
            result = builder.discover_and_include_plugins(temp_dir)

            # Should have discovered one valid plugin and one error
            assert len(result.discovered_plugins) == 1
            assert result.successful_discoveries == 1
            assert result.total_manifests_found == 2
            assert result.success_rate == 0.5
            assert result.has_errors
            assert len(result.errors) == 1

            # Valid plugin should be included
            assert result.discovered_plugins[0].name == "valid_plugin"

            # App should still build successfully with the valid plugin
            app = builder.build()
            registered_plugins = app._registered_plugin_classes
            assert "valid_plugin" in registered_plugins
            assert len(registered_plugins) == 1

    def test_discover_and_include_reference_app_plugins(self) -> None:
        """Test discovery with actual Reference App plugins."""
        reference_plugins_dir = Path("examples/ai-chat-reference/plugins")

        if not reference_plugins_dir.exists():
            pytest.skip("Reference app plugins directory not found")

        builder = PluggingerAppBuilder("discovery_test_app")
        result = builder.discover_and_include_plugins(reference_plugins_dir)

        # Should discover the reference app plugins
        assert result.successful_discoveries >= 3  # memory_store, chat_ai, web_api
        assert not result.has_errors or len(result.errors) == 0

        # Verify specific plugins were discovered
        plugin_names = [p.name for p in result.discovered_plugins]
        assert "memory_store" in plugin_names
        assert "chat_ai" in plugin_names
        assert "web_api" in plugin_names

        # App should build successfully
        app = builder.build()
        services = app.list_services()

        # Should have services from discovered plugins
        memory_services = [s for s in services if s.startswith("memory_store.")]
        chat_services = [s for s in services if s.startswith("chat_ai.")]
        web_services = [s for s in services if s.startswith("web_api.")]

        assert len(memory_services) >= 5  # Expected memory store services
        assert len(chat_services) >= 2   # Expected chat AI services
        assert len(web_services) >= 1    # Expected web API services
