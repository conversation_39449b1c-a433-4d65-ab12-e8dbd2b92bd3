# src/plugginger/core/error_handler.py

"""
Centralized error handling and reporting for the Plugginger framework.

This module provides the ErrorHandler class which consolidates error handling
across the framework, providing structured error reporting, analysis, and
actionable suggestions for developers and AI agents.
"""

from __future__ import annotations

import logging
from collections import defaultdict
from typing import Any, NoReturn

from plugginger.core.exceptions import ErrorCategory, PluggingerError


class ErrorHandler:
    """
    Centralized error handling and reporting system.

    This class provides comprehensive error handling capabilities including:
    - Structured error logging with context
    - Error categorization and analysis
    - Suggestion generation for common issues
    - Error history tracking for debugging
    - AI-Agent compatible error reporting
    """

    def __init__(self, logger: logging.Logger) -> None:
        """
        Initialize the ErrorHandler.

        Args:
            logger: Logger instance for error reporting.
        """
        self.logger = logger
        self.error_history: list[dict[str, Any]] = []
        self._error_counts: dict[str, int] = defaultdict(int)

    def handle_error(
        self,
        error: Exception,
        operation: str,
        context: dict[str, Any] | None = None
    ) -> NoReturn:
        """
        Handle and log error with full context.

        This method processes any exception, converts it to structured format,
        logs it appropriately, and stores it for analysis. If the error is not
        already a PluggingerError, it will be wrapped with additional context.

        Args:
            error: The exception that occurred.
            operation: Description of the operation that failed.
            context: Additional context information about the operation.

        Raises:
            PluggingerError: Always re-raises the error (possibly enhanced) after handling.
        """
        operation_context = context or {}

        if isinstance(error, PluggingerError):
            error_data = error.to_dict()
            # Enhance context with operation context
            if error_data.get("context") is None:
                error_data["context"] = {}
            error_data["context"].update(operation_context)
        else:
            # Convert generic exceptions to structured format
            error_data = self._convert_generic_error(error, operation, operation_context)

        # Add operation context
        error_data["operation"] = operation
        error_data["operation_context"] = operation_context

        # Store for analysis
        self.error_history.append(error_data)
        self._error_counts[error_data["category"]] += 1

        # Log with appropriate level based on category
        self._log_error(error_data)

        # Re-raise with enhanced context
        if isinstance(error, PluggingerError):
            # Enhance the original error with operation context
            if error.context is None:
                error.context = {}
            error.context.update(operation_context)
            raise error
        else:
            raise PluggingerError(
                message=str(error),
                error_code="GENERIC_ERROR",
                category=ErrorCategory.RUNTIME_ERROR,
                context={
                    "original_error_type": type(error).__name__,
                    "operation": operation,
                    **operation_context
                },
                suggestion="Check the operation context and original error details"
            ) from error

    def _convert_generic_error(
        self,
        error: Exception,
        operation: str,
        context: dict[str, Any]
    ) -> dict[str, Any]:
        """
        Convert a generic exception to structured error format.

        Args:
            error: The generic exception.
            operation: The operation that failed.
            context: Additional context.

        Returns:
            Structured error data dictionary.
        """
        # Determine category based on error type and context
        category = self._categorize_generic_error(error, operation, context)

        return {
            "error_code": f"GENERIC_{type(error).__name__.upper()}",
            "category": category.value,
            "message": str(error),
            "context": {
                "original_error_type": type(error).__name__,
                "operation": operation,
                **context
            },
            "suggestion": self._generate_suggestion_for_generic_error(error, operation),
            "related_docs": None,
            "timestamp": None,  # Will be set by PluggingerError if re-raised
            "stack_trace": None  # Will be set by PluggingerError if re-raised
        }

    def _categorize_generic_error(
        self,
        error: Exception,
        operation: str,
        context: dict[str, Any]
    ) -> ErrorCategory:
        """
        Categorize a generic error based on its type and context.

        Args:
            error: The exception to categorize.
            operation: The operation that failed.
            context: Additional context.

        Returns:
            Appropriate ErrorCategory for the error.
        """
        error_type = type(error).__name__

        # Configuration-related errors
        if error_type in ("ValueError", "KeyError") and "config" in operation.lower():
            return ErrorCategory.CONFIGURATION

        # Import/dependency errors
        if error_type in ("ImportError", "ModuleNotFoundError"):
            return ErrorCategory.DEPENDENCY_ERROR

        # User input errors
        if error_type in ("ValueError", "TypeError") and any(
            keyword in operation.lower() for keyword in ["include", "register", "build"]
        ):
            return ErrorCategory.USER_INPUT

        # Default to runtime error
        return ErrorCategory.RUNTIME_ERROR

    def _generate_suggestion_for_generic_error(
        self,
        error: Exception,
        operation: str
    ) -> str:
        """
        Generate actionable suggestions for generic errors.

        Args:
            error: The exception.
            operation: The operation that failed.

        Returns:
            Actionable suggestion string.
        """
        error_type = type(error).__name__

        if error_type == "ImportError":
            return "Check if the required module is installed and accessible"
        elif error_type == "ValueError" and "config" in operation.lower():
            return "Verify configuration values and format"
        elif error_type == "TypeError":
            return "Check method arguments and types"
        elif error_type == "KeyError":
            return "Ensure all required keys are present in the configuration"
        else:
            return f"Review the {operation} operation and check for common issues"

    def _log_error(self, error_data: dict[str, Any]) -> None:
        """
        Log error with appropriate level based on category.

        Args:
            error_data: Structured error data.
        """
        category = error_data["category"]
        message = f"[{error_data['error_code']}] {error_data['message']}"

        # Create extra data without conflicting keys
        extra_data = {k: v for k, v in error_data.items() if k not in ['message', 'msg']}
        extra_data['error_details'] = error_data  # Include full error data under different key

        if category == ErrorCategory.FRAMEWORK_BUG.value:
            self.logger.error(f"Framework Bug Detected: {message}", extra=extra_data)
        elif category == ErrorCategory.USER_INPUT.value:
            self.logger.warning(f"User Input Error: {message}", extra=extra_data)
        elif category == ErrorCategory.CONFIGURATION.value:
            self.logger.warning(f"Configuration Error: {message}", extra=extra_data)
        else:
            self.logger.error(f"Error Occurred: {message}", extra=extra_data)

    def get_error_summary(self) -> dict[str, Any]:
        """
        Get comprehensive error summary for debugging and analysis.

        Returns:
            Dictionary containing error statistics, recent errors, and suggestions.
        """
        return {
            "total_errors": len(self.error_history),
            "by_category": dict(self._error_counts),
            "recent_errors": self.error_history[-5:] if self.error_history else [],
            "suggestions": self._generate_summary_suggestions(),
            "most_common_errors": self._get_most_common_errors()
        }

    def _generate_summary_suggestions(self) -> list[str]:
        """
        Generate suggestions based on error patterns.

        Returns:
            List of actionable suggestions.
        """
        suggestions = []

        if self._error_counts[ErrorCategory.DEPENDENCY_ERROR.value] > 0:
            suggestions.append("Review plugin dependencies and ensure all required plugins are registered")

        if self._error_counts[ErrorCategory.CONFIGURATION.value] > 0:
            suggestions.append("Validate configuration format and required fields")

        if self._error_counts[ErrorCategory.PLUGIN_ERROR.value] > 0:
            suggestions.append("Plugin registration errors detected - ensure plugins use @plugin decorator and inherit from PluginBase")

        if self._error_counts[ErrorCategory.USER_INPUT.value] > 2:
            suggestions.append("Multiple user input errors detected - review plugin registration and usage")

        if len(self.error_history) > 10:
            suggestions.append("High error count detected - consider reviewing application architecture")

        return suggestions

    def _get_most_common_errors(self) -> list[dict[str, Any]]:
        """
        Get the most common error types for analysis.

        Returns:
            List of error type summaries.
        """
        error_type_counts: dict[str, int] = defaultdict(int)

        for error in self.error_history:
            error_code = error.get("error_code", "UNKNOWN")
            error_type_counts[error_code] += 1

        # Sort by frequency and return top 5
        sorted_errors = sorted(error_type_counts.items(), key=lambda x: x[1], reverse=True)

        return [
            {"error_code": error_code, "count": count}
            for error_code, count in sorted_errors[:5]
        ]

    def clear_history(self) -> None:
        """Clear error history and reset counters."""
        self.error_history.clear()
        self._error_counts.clear()
