# Plugginger Repository Management Scripts

Diese Scripts implementieren die Multi-Level Backup- und Rollback-Strategie aus der ROADMAP.md.

## 🔒 Backup Script (`backup.sh`)

Erstellt gestaffelte Backups für verschiedene Entwicklungsphasen.

### Verwendung

```bash
# Standard Task-Backup (vor jeder Entwicklung)
./scripts/backup.sh current

# Sprint-Start Backup
./scripts/backup.sh sprint "Sprint 3 Framework Hardening"

# Meilenstein-Backup
./scripts/backup.sh milestone "v0.9.0 Release"

# Notfall-Backup (vor riskanten Änderungen)
./scripts/backup.sh emergency "Before major refactoring"

# Alte Backups aufräumen
./scripts/backup.sh cleanup
```

### Features

- ✅ Quality Gates Check vor Backup-Erstellung
- ✅ Automatische Cleanup alter Emergency-Backups
- ✅ Überschreibt `backup/current` für tägliche Arbeit
- ✅ Behält Sprint- und Milestone-Backups langfristig
- ✅ Farbige Ausgabe für bessere Übersicht

## 🔄 Rollback Script (`rollback.sh`)

Intelligente Rollback-Funktionen mit Schadens-Analyse.

### Verwendung

```bash
# Automatische Schadens-Erkennung
./scripts/rollback.sh auto "KI beschädigte Repository"

# Manueller Rollback nach Level
./scripts/rollback.sh 1 "Einzelner Test fehlgeschlagen"
./scripts/rollback.sh 3 "Framework schwer beschädigt"

# Verfügbare Backups anzeigen
./scripts/rollback.sh list
```

### Rollback-Level

| Level | Schäden | Rollback-Ziel | Datenverlust |
|-------|---------|---------------|--------------|
| **1** | 1-5 Tests fehlgeschlagen | `backup/current` | <30 Min |
| **2** | Build-Fehler, >10 Tests | `backup/current` | <2h |
| **3** | >50% Tests fehlgeschlagen | `backup/sprint-N-start` | Aktueller Sprint |
| **4** | Repository unbenutzbar | `backup/milestone-YYYY-MM-DD` | Mehrere Sprints |

### Features

- ✅ Automatische Schadens-Analyse (pytest/mypy/ruff)
- ✅ Intelligente Level-Empfehlung
- ✅ Notfall-Backup vor Rollback
- ✅ Dokumentation in Commit-Message
- ✅ Sicherheits-Bestätigung erforderlich

## 📋 Backup-Hierarchie

```
Repository Backups (max. 8 Branches)
├── backup/current              # Letzter stabiler Zustand
├── backup/sprint-N-start       # Sprint-Anfangszustände (max. 3)
├── backup/milestone-YYYY-MM-DD # Wichtige Meilensteine
├── backup/emergency-YYYY-MM-DD-HH-MM # Notfall-Snapshots (max. 10)
└── [Arbeits-Branches]          # Temporäre Entwicklung
```

## 🚨 Notfall-Prozedur

### Bei Repository-Schäden:

1. **Sofort-Analyse**:
   ```bash
   ./scripts/rollback.sh auto "Beschreibung des Problems"
   ```

2. **Manuelle Intervention** (falls auto fehlschlägt):
   ```bash
   ./scripts/rollback.sh list
   ./scripts/rollback.sh 3 "Manuelle Wiederherstellung"
   ```

3. **Ursachen-Analyse** nach Rollback:
   - Commit-History analysieren
   - Fehlerhafte Änderungen identifizieren
   - Präventionsmaßnahmen implementieren

### Bei Backup-Verlust:

1. **GitHub Repository Restore**:
   - GitHub → Settings → Restore
   - Letzten funktionsfähigen Commit wählen

2. **Lokale Wiederherstellung**:
   ```bash
   git reflog  # Lokale Commit-History
   git reset --hard <commit-hash>
   ```

## 🤖 Integration in KI-Workflow

### Vor jedem Task:
```bash
./scripts/backup.sh current
```

### Bei Problemen:
```bash
./scripts/rollback.sh auto "KI-Agent Fehler"
```

### Sprint-Start:
```bash
./scripts/backup.sh sprint "Sprint N Start"
```

### Nach großen Erfolgen:
```bash
./scripts/backup.sh milestone "Feature X completed"
```

## 📊 Monitoring

### Backup-Status prüfen:
```bash
git branch -r | grep backup
```

### Repository-Gesundheit:
```bash
pytest --tb=no -q && mypy --strict src/ && ruff check
```

### Letzte Backups:
```bash
./scripts/rollback.sh list
```

## 🔧 Konfiguration

Scripts können über Umgebungsvariablen angepasst werden:

```bash
export MAX_EMERGENCY_BACKUPS=15  # Standard: 10
export MAX_SPRINT_BACKUPS=5      # Standard: 3
```

## 📚 Siehe auch

- `ROADMAP.md` - Vollständige Backup-Strategie
- `docs/BRANCHING_STRATEGY.md` - Detaillierte Branch-Regeln
- `.github/workflows/` - Geplante Automatisierung

---

**Wichtig**: Diese Scripts sind Teil der kritischen Repository-Infrastruktur. Änderungen nur nach RFC-Prozess!
