"""
Unit tests for PluginDiscovery class.

Tests the core plugin discovery functionality including directory scanning,
manifest loading, plugin class loading, and error handling.
"""

from __future__ import annotations

import tempfile
from pathlib import Path

from plugginger.discovery.discovery import PluginDiscovery
from plugginger.discovery.models import DiscoveredPlugin, DiscoveryResult


class TestPluginDiscovery:
    """Test cases for PluginDiscovery class."""

    def test_discover_in_directory_empty_directory(self) -> None:
        """Test discovery in an empty directory."""
        with tempfile.TemporaryDirectory() as temp_dir:
            discovery = PluginDiscovery()
            result = discovery.discover_in_directory(temp_dir)

            assert isinstance(result, DiscoveryResult)
            assert len(result.discovered_plugins) == 0
            assert result.total_manifests_found == 0
            assert result.successful_discoveries == 0
            assert result.success_rate == 1.0  # No manifests = 100% success
            assert not result.has_errors

    def test_discover_in_directory_nonexistent(self) -> None:
        """Test discovery in a nonexistent directory."""
        discovery = PluginDiscovery()
        result = discovery.discover_in_directory("/nonexistent/directory")

        assert isinstance(result, DiscoveryResult)
        assert len(result.discovered_plugins) == 0
        assert result.total_manifests_found == 0
        assert result.successful_discoveries == 0
        assert result.has_errors
        assert "Directory not found" in result.errors[0]

    def test_discover_in_directory_file_instead_of_directory(self) -> None:
        """Test discovery when path points to a file instead of directory."""
        with tempfile.NamedTemporaryFile() as temp_file:
            discovery = PluginDiscovery()
            result = discovery.discover_in_directory(temp_file.name)

            assert isinstance(result, DiscoveryResult)
            assert len(result.discovered_plugins) == 0
            assert result.has_errors
            assert "Path is not a directory" in result.errors[0]

    def test_discover_plugin_with_valid_manifest_and_plugin(self) -> None:
        """Test successful discovery of a plugin with valid manifest and plugin file."""
        with tempfile.TemporaryDirectory() as temp_dir:
            plugin_dir = Path(temp_dir) / "test_plugin"
            plugin_dir.mkdir()

            # Create manifest file
            manifest_content = """
manifest_version: "1.0.0"
metadata:
  name: "test_plugin"
  version: "1.0.0"
  description: "Test plugin for discovery"
  author: "Test Author"
  license: "MIT"
  keywords: ["test"]

runtime:
  plugginger_version: ">=0.9.0"
  execution_mode: "thread"

services: []
event_listeners: []
dependencies: []
"""
            manifest_path = plugin_dir / "manifest.yaml"
            manifest_path.write_text(manifest_content)

            # Create plugin file
            plugin_content = '''
from plugginger.api.plugin import PluginBase, plugin

@plugin(name="test_plugin", version="1.0.0")
class TestPlugin(PluginBase):
    """Test plugin for discovery."""
    pass
'''
            plugin_file = plugin_dir / "plugin.py"
            plugin_file.write_text(plugin_content)

            # Test discovery
            discovery = PluginDiscovery()
            result = discovery.discover_in_directory(temp_dir)

            assert len(result.discovered_plugins) == 1
            assert result.total_manifests_found == 1
            assert result.successful_discoveries == 1
            assert result.success_rate == 1.0
            assert not result.has_errors

            plugin = result.discovered_plugins[0]
            assert isinstance(plugin, DiscoveredPlugin)
            assert plugin.name == "test_plugin"
            assert plugin.plugin_class.__name__ == "TestPlugin"
            assert plugin.manifest.metadata.name == "test_plugin"
            assert plugin.manifest.metadata.version == "1.0.0"
            assert plugin.manifest_path == manifest_path
            assert plugin.module_path == plugin_dir

    def test_discover_plugin_with_init_py(self) -> None:
        """Test discovery of a plugin using __init__.py instead of plugin.py."""
        with tempfile.TemporaryDirectory() as temp_dir:
            plugin_dir = Path(temp_dir) / "test_plugin"
            plugin_dir.mkdir()

            # Create manifest file
            manifest_content = """
manifest_version: "1.0.0"
metadata:
  name: "init_plugin"
  version: "1.0.0"
runtime:
  plugginger_version: ">=0.9.0"
services: []
event_listeners: []
dependencies: []
"""
            manifest_path = plugin_dir / "manifest.yaml"
            manifest_path.write_text(manifest_content)

            # Create __init__.py file
            plugin_content = '''
from plugginger.api.plugin import PluginBase, plugin

@plugin(name="init_plugin", version="1.0.0")
class InitPlugin(PluginBase):
    """Test plugin using __init__.py."""
    pass
'''
            plugin_file = plugin_dir / "__init__.py"
            plugin_file.write_text(plugin_content)

            # Test discovery
            discovery = PluginDiscovery()
            result = discovery.discover_in_directory(temp_dir)

            assert len(result.discovered_plugins) == 1
            plugin = result.discovered_plugins[0]
            assert plugin.name == "init_plugin"
            assert plugin.plugin_class.__name__ == "InitPlugin"

    def test_discover_plugin_missing_plugin_file(self) -> None:
        """Test discovery when manifest exists but plugin file is missing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            plugin_dir = Path(temp_dir) / "test_plugin"
            plugin_dir.mkdir()

            # Create manifest file only
            manifest_content = """
manifest_version: "1.0.0"
metadata:
  name: "missing_plugin"
  version: "1.0.0"
runtime:
  plugginger_version: ">=0.9.0"
services: []
event_listeners: []
dependencies: []
"""
            manifest_path = plugin_dir / "manifest.yaml"
            manifest_path.write_text(manifest_content)

            # Test discovery
            discovery = PluginDiscovery()
            result = discovery.discover_in_directory(temp_dir)

            assert len(result.discovered_plugins) == 0
            assert result.total_manifests_found == 1
            assert result.successful_discoveries == 0
            assert result.success_rate == 0.0
            assert result.has_errors
            assert "No plugin module found" in result.errors[0]

    def test_discover_plugin_invalid_manifest(self) -> None:
        """Test discovery with invalid manifest file."""
        with tempfile.TemporaryDirectory() as temp_dir:
            plugin_dir = Path(temp_dir) / "test_plugin"
            plugin_dir.mkdir()

            # Create invalid manifest file
            manifest_content = """
invalid_yaml: [unclosed list
missing_required_fields: true
"""
            manifest_path = plugin_dir / "manifest.yaml"
            manifest_path.write_text(manifest_content)

            # Test discovery
            discovery = PluginDiscovery()
            result = discovery.discover_in_directory(temp_dir)

            assert len(result.discovered_plugins) == 0
            assert result.total_manifests_found == 1
            assert result.successful_discoveries == 0
            assert result.has_errors

    def test_discover_plugin_invalid_python_file(self) -> None:
        """Test discovery with invalid Python plugin file."""
        with tempfile.TemporaryDirectory() as temp_dir:
            plugin_dir = Path(temp_dir) / "test_plugin"
            plugin_dir.mkdir()

            # Create valid manifest file
            manifest_content = """
manifest_version: "1.0.0"
metadata:
  name: "invalid_plugin"
  version: "1.0.0"
runtime:
  plugginger_version: ">=0.9.0"
services: []
event_listeners: []
dependencies: []
"""
            manifest_path = plugin_dir / "manifest.yaml"
            manifest_path.write_text(manifest_content)

            # Create invalid Python file
            plugin_content = '''
# Invalid Python syntax
class InvalidPlugin(
    # Missing closing parenthesis and colon
'''
            plugin_file = plugin_dir / "plugin.py"
            plugin_file.write_text(plugin_content)

            # Test discovery
            discovery = PluginDiscovery()
            result = discovery.discover_in_directory(temp_dir)

            assert len(result.discovered_plugins) == 0
            assert result.total_manifests_found == 1
            assert result.successful_discoveries == 0
            assert result.has_errors

    def test_discover_plugin_no_plugin_class(self) -> None:
        """Test discovery when Python file has no plugin classes."""
        with tempfile.TemporaryDirectory() as temp_dir:
            plugin_dir = Path(temp_dir) / "test_plugin"
            plugin_dir.mkdir()

            # Create valid manifest file
            manifest_content = """
manifest_version: "1.0.0"
metadata:
  name: "no_plugin"
  version: "1.0.0"
runtime:
  plugginger_version: ">=0.9.0"
services: []
event_listeners: []
dependencies: []
"""
            manifest_path = plugin_dir / "manifest.yaml"
            manifest_path.write_text(manifest_content)

            # Create Python file without plugin classes
            plugin_content = '''
# Valid Python but no plugin classes
def some_function():
    return "hello"

class RegularClass:
    pass
'''
            plugin_file = plugin_dir / "plugin.py"
            plugin_file.write_text(plugin_content)

            # Test discovery
            discovery = PluginDiscovery()
            result = discovery.discover_in_directory(temp_dir)

            assert len(result.discovered_plugins) == 0
            assert result.total_manifests_found == 1
            assert result.successful_discoveries == 0
            # This should not be an error, just no plugins found
            assert not result.has_errors

    def test_discover_recursive_vs_non_recursive(self) -> None:
        """Test recursive vs non-recursive directory scanning."""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create plugin directly in temp_dir for non-recursive discovery
            manifest_content = """
manifest_version: "1.0.0"
metadata:
  name: "root_plugin"
  version: "1.0.0"
runtime:
  plugginger_version: ">=0.9.0"
services: []
event_listeners: []
dependencies: []
"""
            (Path(temp_dir) / "manifest.yaml").write_text(manifest_content)

            plugin_content = '''
from plugginger.api.plugin import PluginBase, plugin

@plugin(name="root_plugin", version="1.0.0")
class RootPlugin(PluginBase):
    pass
'''
            (Path(temp_dir) / "plugin.py").write_text(plugin_content)

            # Create nested plugin structure
            nested_plugin_dir = Path(temp_dir) / "subdir" / "nested_plugin"
            nested_plugin_dir.mkdir(parents=True)

            nested_manifest_content = """
manifest_version: "1.0.0"
metadata:
  name: "nested_plugin"
  version: "1.0.0"
runtime:
  plugginger_version: ">=0.9.0"
services: []
event_listeners: []
dependencies: []
"""
            (nested_plugin_dir / "manifest.yaml").write_text(nested_manifest_content)

            nested_plugin_content = '''
from plugginger.api.plugin import PluginBase, plugin

@plugin(name="nested_plugin", version="1.0.0")
class NestedPlugin(PluginBase):
    pass
'''
            (nested_plugin_dir / "plugin.py").write_text(nested_plugin_content)

            discovery = PluginDiscovery()

            # Test non-recursive discovery - should only find root plugin
            result_non_recursive = discovery.discover_in_directory(temp_dir, recursive=False)
            assert len(result_non_recursive.discovered_plugins) == 1
            assert result_non_recursive.discovered_plugins[0].name == "root_plugin"

            # Test recursive discovery - should find both plugins
            result_recursive = discovery.discover_in_directory(temp_dir, recursive=True)
            assert len(result_recursive.discovered_plugins) == 2
            plugin_names = [p.name for p in result_recursive.discovered_plugins]
            assert "root_plugin" in plugin_names
            assert "nested_plugin" in plugin_names

    def test_discover_custom_pattern(self) -> None:
        """Test discovery with custom manifest filename pattern."""
        with tempfile.TemporaryDirectory() as temp_dir:
            plugin_dir = Path(temp_dir) / "test_plugin"
            plugin_dir.mkdir()

            # Create manifest with custom name
            manifest_content = """
manifest_version: "1.0.0"
metadata:
  name: "custom_manifest_plugin"
  version: "1.0.0"
runtime:
  plugginger_version: ">=0.9.0"
services: []
event_listeners: []
dependencies: []
"""
            manifest_path = plugin_dir / "plugin.manifest"  # Custom name
            manifest_path.write_text(manifest_content)

            plugin_content = '''
from plugginger.api.plugin import PluginBase, plugin

@plugin(name="custom_manifest_plugin", version="1.0.0")
class CustomManifestPlugin(PluginBase):
    pass
'''
            (plugin_dir / "plugin.py").write_text(plugin_content)

            discovery = PluginDiscovery()

            # Test with default pattern (should find nothing)
            result_default = discovery.discover_in_directory(temp_dir)
            assert len(result_default.discovered_plugins) == 0

            # Test with custom pattern
            result_custom = discovery.discover_in_directory(temp_dir, pattern="plugin.manifest")
            assert len(result_custom.discovered_plugins) == 1
            assert result_custom.discovered_plugins[0].name == "custom_manifest_plugin"
