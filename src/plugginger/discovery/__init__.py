"""
Plugin discovery and automatic inclusion system.

This package provides utilities for automatically discovering and including
plugins based on their manifest files, enabling AI-agent friendly plugin
management and reducing manual configuration overhead.

The discovery system enables:
- Automatic plugin discovery in directories
- Manifest-based plugin inclusion
- Dependency resolution from manifests
- AI-agent readable plugin metadata
- Flexible discovery strategies

Example usage:
    ```python
    from plugginger.discovery import PluginDiscovery
    from plugginger.api.builder import PluggingerAppBuilder

    # Discover plugins in a directory
    discovery = PluginDiscovery()
    plugins = discovery.discover_in_directory("plugins/")

    # Auto-include discovered plugins
    builder = PluggingerAppBuilder("my_app")
    builder.enable_manifest_loading()
    discovery.include_discovered_plugins(builder, plugins)

    app = builder.build()
    ```
"""

from plugginger.discovery.discovery import PluginDiscovery
from plugginger.discovery.models import DiscoveredPlugin, DiscoveryResult

__all__ = ["PluginDiscovery", "DiscoveredPlugin", "DiscoveryResult"]
