#!/bin/bash
# Plugginger Repository Backup Script
# Implements Multi-Level Backup Strategy from ROADMAP.md
# Usage: ./scripts/backup.sh [level] [description]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
MAX_EMERGENCY_BACKUPS=10
MAX_SPRINT_BACKUPS=3
CURRENT_DATE=$(date +%Y-%m-%d)
CURRENT_TIME=$(date +%H-%M)

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_git_status() {
    if [[ -n $(git status --porcelain) ]]; then
        log_warning "Repository has uncommitted changes. Consider committing first."
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

check_quality_gates() {
    log_info "Checking quality gates before backup..."
    
    # Check if we're in the right directory
    if [[ ! -f "pyproject.toml" ]] || [[ ! -d "src/plugginger" ]]; then
        log_error "Not in Plugginger repository root!"
        exit 1
    fi
    
    # Run quality checks
    log_info "Running pytest..."
    if ! pytest --tb=no -q > /dev/null 2>&1; then
        log_warning "Tests are failing! This backup may contain broken code."
        read -p "Create backup anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
    
    log_info "Running mypy..."
    if ! mypy --strict src/ > /dev/null 2>&1; then
        log_warning "MyPy errors detected!"
    fi
    
    log_info "Running ruff..."
    if ! ruff check > /dev/null 2>&1; then
        log_warning "Ruff errors detected!"
    fi
}

create_backup() {
    local backup_name=$1
    local description=$2
    
    log_info "Creating backup: $backup_name"
    
    # Ensure we're on main
    git checkout main
    git pull origin main
    
    # Delete existing backup if it exists
    if git show-ref --verify --quiet refs/heads/$backup_name; then
        log_info "Deleting existing local branch: $backup_name"
        git branch -D $backup_name
    fi
    
    if git ls-remote --heads origin $backup_name | grep -q $backup_name; then
        log_info "Deleting existing remote branch: $backup_name"
        git push origin --delete $backup_name
    fi
    
    # Create new backup
    git checkout -b $backup_name
    git push origin $backup_name
    git checkout main
    
    log_success "Backup created: $backup_name"
    if [[ -n "$description" ]]; then
        log_info "Description: $description"
    fi
}

cleanup_old_backups() {
    log_info "Cleaning up old backups..."
    
    # Clean emergency backups (keep only last 10)
    local emergency_branches=$(git branch -r | grep "origin/backup/emergency" | sed 's/origin\///' | sort -r)
    local emergency_count=$(echo "$emergency_branches" | wc -l)
    
    if [[ $emergency_count -gt $MAX_EMERGENCY_BACKUPS ]]; then
        local to_delete=$(echo "$emergency_branches" | tail -n +$((MAX_EMERGENCY_BACKUPS + 1)))
        for branch in $to_delete; do
            log_info "Deleting old emergency backup: $branch"
            git push origin --delete $branch 2>/dev/null || true
        done
    fi
    
    # Clean sprint backups (keep only last 3)
    local sprint_branches=$(git branch -r | grep "origin/backup/sprint" | sed 's/origin\///' | sort -r)
    local sprint_count=$(echo "$sprint_branches" | wc -l)
    
    if [[ $sprint_count -gt $MAX_SPRINT_BACKUPS ]]; then
        local to_delete=$(echo "$sprint_branches" | tail -n +$((MAX_SPRINT_BACKUPS + 1)))
        for branch in $to_delete; do
            log_info "Deleting old sprint backup: $branch"
            git push origin --delete $branch 2>/dev/null || true
        done
    fi
}

show_usage() {
    echo "Plugginger Backup Script"
    echo "Usage: $0 [LEVEL] [DESCRIPTION]"
    echo ""
    echo "Backup Levels:"
    echo "  current     - Standard task backup (overwrites previous)"
    echo "  sprint      - Sprint start backup"
    echo "  milestone   - Important milestone backup"
    echo "  emergency   - Emergency backup with timestamp"
    echo "  cleanup     - Clean up old backups only"
    echo ""
    echo "Examples:"
    echo "  $0 current"
    echo "  $0 sprint \"Sprint 3 start\""
    echo "  $0 milestone \"v0.9.0 release\""
    echo "  $0 emergency \"Before risky refactoring\""
    echo "  $0 cleanup"
}

# Main script
if [[ $# -eq 0 ]]; then
    show_usage
    exit 1
fi

LEVEL=$1
DESCRIPTION=${2:-""}

case $LEVEL in
    "current")
        check_git_status
        check_quality_gates
        create_backup "backup/current" "$DESCRIPTION"
        ;;
    "sprint")
        check_git_status
        check_quality_gates
        # Auto-detect sprint number or use description
        if [[ -z "$DESCRIPTION" ]]; then
            SPRINT_NUM=$(git branch -r | grep "backup/sprint" | wc -l)
            SPRINT_NUM=$((SPRINT_NUM + 1))
            BACKUP_NAME="backup/sprint-${SPRINT_NUM}-start"
        else
            BACKUP_NAME="backup/sprint-${CURRENT_DATE}"
        fi
        create_backup "$BACKUP_NAME" "$DESCRIPTION"
        ;;
    "milestone")
        check_git_status
        check_quality_gates
        create_backup "backup/milestone-${CURRENT_DATE}" "$DESCRIPTION"
        ;;
    "emergency")
        check_git_status
        create_backup "backup/emergency-${CURRENT_DATE}-${CURRENT_TIME}" "$DESCRIPTION"
        cleanup_old_backups
        ;;
    "cleanup")
        cleanup_old_backups
        ;;
    *)
        log_error "Unknown backup level: $LEVEL"
        show_usage
        exit 1
        ;;
esac

log_success "Backup operation completed!"
