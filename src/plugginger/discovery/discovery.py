"""
Plugin discovery implementation.

This module provides the core plugin discovery functionality, allowing
automatic discovery and inclusion of plugins based on their manifest files.
"""

from __future__ import annotations

import importlib.util
import logging
import sys
from pathlib import Path
from typing import TYPE_CHECKING, Any

from plugginger.core.exceptions import PluginRegistrationError
from plugginger.discovery.models import DiscoveredPlugin, DiscoveryResult
from plugginger.manifest.converter import ManifestConverter

if TYPE_CHECKING:
    from plugginger.api.builder import PluggingerAppBuilder


class PluginDiscovery:
    """
    Plugin discovery service for automatic plugin inclusion.

    This class provides functionality to automatically discover plugins
    in directories based on their manifest files, load their classes,
    and include them in PluggingerAppBuilder instances.
    """

    def __init__(self, logger: logging.Logger | None = None) -> None:
        """
        Initialize the plugin discovery service.

        Args:
            logger: Optional logger for debugging and error reporting
        """
        self._logger = logger or logging.getLogger(__name__)
        self._manifest_converter = ManifestConverter(logger)

    def discover_in_directory(
        self,
        directory: str | Path,
        recursive: bool = True,
        pattern: str = "manifest.yaml"
    ) -> DiscoveryResult:
        """
        Discover plugins in a directory by scanning for manifest files.

        Args:
            directory: Directory to scan for plugins
            recursive: Whether to scan subdirectories recursively
            pattern: Filename pattern for manifest files

        Returns:
            DiscoveryResult containing discovered plugins and any errors
        """
        directory_path = Path(directory)

        if not directory_path.exists():
            return DiscoveryResult(
                discovered_plugins=[],
                errors=[f"Directory not found: {directory_path}"],
                discovery_source=str(directory_path),
                total_manifests_found=0,
                successful_discoveries=0
            )

        if not directory_path.is_dir():
            return DiscoveryResult(
                discovered_plugins=[],
                errors=[f"Path is not a directory: {directory_path}"],
                discovery_source=str(directory_path),
                total_manifests_found=0,
                successful_discoveries=0
            )

        self._logger.info(f"Discovering plugins in directory: {directory_path}")

        # Find all manifest files
        if recursive:
            manifest_files = list(directory_path.rglob(pattern))
        else:
            manifest_files = list(directory_path.glob(pattern))

        discovered_plugins: list[DiscoveredPlugin] = []
        errors: list[str] = []

        self._logger.debug(f"Found {len(manifest_files)} manifest files")

        for manifest_path in manifest_files:
            try:
                plugin = self._discover_plugin_from_manifest(manifest_path, str(directory_path))
                if plugin:
                    discovered_plugins.append(plugin)
                    self._logger.info(f"Successfully discovered plugin: {plugin.name}")
            except Exception as e:
                error_msg = f"Failed to discover plugin from {manifest_path}: {e}"
                errors.append(error_msg)
                self._logger.error(error_msg)

        result = DiscoveryResult(
            discovered_plugins=discovered_plugins,
            errors=errors,
            discovery_source=str(directory_path),
            total_manifests_found=len(manifest_files),
            successful_discoveries=len(discovered_plugins)
        )

        self._logger.info(f"Discovery completed: {result}")
        return result

    def _discover_plugin_from_manifest(
        self,
        manifest_path: Path,
        discovery_source: str
    ) -> DiscoveredPlugin | None:
        """
        Discover a single plugin from its manifest file.

        Args:
            manifest_path: Path to the manifest file
            discovery_source: Source identifier for discovery tracking

        Returns:
            DiscoveredPlugin if successful, None if plugin class not found

        Raises:
            Exception: If manifest loading or plugin class loading fails
        """
        self._logger.debug(f"Processing manifest: {manifest_path}")

        # Load and convert manifest
        manifest = self._manifest_converter.yaml_to_pydantic(manifest_path)

        # Determine plugin module directory
        plugin_dir = manifest_path.parent

        # Look for plugin.py or __init__.py in the same directory
        plugin_module_path = None
        for candidate in ["plugin.py", "__init__.py"]:
            candidate_path = plugin_dir / candidate
            if candidate_path.exists():
                plugin_module_path = candidate_path
                break

        if not plugin_module_path:
            raise PluginRegistrationError(
                f"No plugin module found in {plugin_dir}. "
                f"Expected 'plugin.py' or '__init__.py'"
            )

        # Load the plugin module
        plugin_class = self._load_plugin_class_from_file(
            plugin_module_path,
            manifest.metadata.name
        )

        if not plugin_class:
            return None

        return DiscoveredPlugin(
            name=manifest.metadata.name,
            plugin_class=plugin_class,
            manifest=manifest,
            manifest_path=manifest_path,
            module_path=plugin_dir,
            discovery_source=discovery_source
        )

    def _load_plugin_class_from_file(
        self,
        module_path: Path,
        expected_plugin_name: str
    ) -> type[Any] | None:
        """
        Load a plugin class from a Python file.

        Args:
            module_path: Path to the Python module file
            expected_plugin_name: Expected plugin name for validation

        Returns:
            Plugin class if found, None otherwise

        Raises:
            Exception: If module loading fails
        """
        # Create a unique module name to avoid conflicts
        module_name = f"discovered_plugin_{expected_plugin_name}_{id(module_path)}"

        # Load the module
        spec = importlib.util.spec_from_file_location(module_name, module_path)
        if not spec or not spec.loader:
            raise PluginRegistrationError(f"Cannot load module spec from {module_path}")

        module = importlib.util.module_from_spec(spec)

        # Add to sys.modules to support relative imports
        sys.modules[module_name] = module

        try:
            spec.loader.exec_module(module)
        except Exception as e:
            # Clean up on failure
            if module_name in sys.modules:
                del sys.modules[module_name]
            raise PluginRegistrationError(f"Failed to execute module {module_path}: {e}") from e

        # Find plugin classes in the module
        plugin_classes = []
        for attr_name in dir(module):
            attr = getattr(module, attr_name)
            if (
                isinstance(attr, type) and
                hasattr(attr, '_plugginger_plugin_name') and
                not attr_name.startswith('_')
            ):
                plugin_classes.append(attr)

        if not plugin_classes:
            # Clean up
            if module_name in sys.modules:
                del sys.modules[module_name]
            self._logger.warning(f"No plugin classes found in {module_path}")
            return None

        if len(plugin_classes) > 1:
            self._logger.warning(
                f"Multiple plugin classes found in {module_path}: "
                f"{[cls.__name__ for cls in plugin_classes]}. Using first one."
            )

        plugin_class = plugin_classes[0]

        # Validate plugin name matches manifest
        plugin_name = getattr(plugin_class, '_plugginger_plugin_name', None)
        if plugin_name != expected_plugin_name:
            self._logger.warning(
                f"Plugin name mismatch: manifest says '{expected_plugin_name}', "
                f"class says '{plugin_name}'. Using manifest name."
            )

        return plugin_class

    def include_discovered_plugins(
        self,
        builder: PluggingerAppBuilder,
        discovery_result: DiscoveryResult
    ) -> None:
        """
        Include all discovered plugins in a PluggingerAppBuilder.

        Args:
            builder: The builder to include plugins in
            discovery_result: Result from a discovery operation

        Raises:
            PluginRegistrationError: If plugin inclusion fails
        """
        if not discovery_result.discovered_plugins:
            self._logger.info("No plugins to include from discovery result")
            return

        self._logger.info(
            f"Including {len(discovery_result.discovered_plugins)} discovered plugins"
        )

        for plugin in discovery_result.discovered_plugins:
            try:
                self._logger.debug(f"Including plugin: {plugin.name}")
                builder.include(plugin.plugin_class)
                self._logger.info(f"Successfully included plugin: {plugin.name}")
            except Exception as e:
                error_msg = f"Failed to include discovered plugin '{plugin.name}': {e}"
                self._logger.error(error_msg)
                raise PluginRegistrationError(error_msg) from e

    def discover_and_include(
        self,
        builder: PluggingerAppBuilder,
        directory: str | Path,
        recursive: bool = True,
        pattern: str = "manifest.yaml"
    ) -> DiscoveryResult:
        """
        Convenience method to discover and include plugins in one step.

        Args:
            builder: The builder to include plugins in
            directory: Directory to scan for plugins
            recursive: Whether to scan subdirectories recursively
            pattern: Filename pattern for manifest files

        Returns:
            DiscoveryResult containing discovered plugins and any errors

        Raises:
            PluginRegistrationError: If plugin inclusion fails
        """
        discovery_result = self.discover_in_directory(directory, recursive, pattern)

        if discovery_result.discovered_plugins:
            self.include_discovered_plugins(builder, discovery_result)

        return discovery_result
