"""
Unit tests for the ManifestConverter class.

Tests the conversion of user-friendly YAML manifests to framework
Pydantic format, ensuring proper handling of all manifest sections
and error cases.
"""

from __future__ import annotations

import tempfile
from pathlib import Path

import pytest

from plugginger.core.exceptions import PluginRegistrationError
from plugginger.manifest.converter import ManifestConverter
from plugginger.schemas.manifest import (
    DependencyInfo,
    EventListenerInfo,
    ParameterKind,
    PluginManifest,
    ServiceInfo,
)


class TestManifestConverter:
    """Test cases for ManifestConverter class."""

    def test_yaml_to_pydantic_basic_conversion(self) -> None:
        """Test basic YAML to Pydantic conversion."""
        yaml_content = """
manifest_version: "1.0.0"
metadata:
  name: "test_plugin"
  version: "1.0.0"
  description: "Test plugin"
  author: "Test Author"
  license: "MIT"
  tags: ["test", "example"]

runtime:
  plugginger_version: ">=0.9.0"
  execution_mode: "thread"
  python_version: ">=3.11"

services:
  - name: "test_service"
    description: "Test service method"
    parameters:
      - name: "param1"
        type: "str"
        required: true
        description: "First parameter"
      - name: "param2"
        type: "int"
        required: false
        default: 42
        description: "Second parameter"
    returns:
      type: "str"
      description: "Return value"

event_listeners:
  - event_pattern: "test.event"
    handler: "handle_test_event"
    description: "Handle test events"
    priority: 10

dependencies:
  - name: "other_plugin"
    version: ">=1.0.0"
    optional: false
    description: "Required dependency"

config_schema:
  type: "object"
  properties:
    setting1:
      type: "string"
      default: "value1"
"""

        converter = ManifestConverter()

        # Create temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            f.write(yaml_content)
            temp_path = f.name

        try:
            manifest = converter.yaml_to_pydantic(temp_path)

            # Verify manifest structure
            assert isinstance(manifest, PluginManifest)
            assert manifest.manifest_version == "1.0.0"

            # Verify metadata
            assert manifest.metadata.name == "test_plugin"
            assert manifest.metadata.version == "1.0.0"
            assert manifest.metadata.description == "Test plugin"
            assert manifest.metadata.author == "Test Author"
            assert manifest.metadata.license == "MIT"
            assert manifest.metadata.keywords == ["test", "example"]  # tags -> keywords

            # Verify runtime
            assert manifest.runtime.plugginger_version == ">=0.9.0"
            assert manifest.runtime.execution_mode.value == "thread"
            assert manifest.runtime.python_version == ">=3.11"

            # Verify services
            assert len(manifest.services) == 1
            service = manifest.services[0]
            assert isinstance(service, ServiceInfo)
            assert service.name == "test_service"
            assert service.method_name == "test_service"
            assert service.description == "Test service method"
            assert service.return_annotation == "str"
            assert "def test_service(" in service.signature
            assert "-> str" in service.signature

            # Verify service parameters
            assert len(service.parameters) == 2
            param1 = service.parameters[0]
            assert param1.name == "param1"
            assert param1.annotation == "str"
            assert param1.kind == ParameterKind.POSITIONAL_OR_KEYWORD
            assert param1.default is None

            param2 = service.parameters[1]
            assert param2.name == "param2"
            assert param2.annotation == "int"
            assert param2.kind == ParameterKind.KEYWORD_ONLY
            assert param2.default == 42

            # Verify event listeners
            assert len(manifest.event_listeners) == 1
            listener = manifest.event_listeners[0]
            assert isinstance(listener, EventListenerInfo)
            assert listener.patterns == ["test.event"]
            assert listener.method_name == "handle_test_event"
            assert listener.description == "Handle test events"
            assert listener.priority == 10
            assert "def handle_test_event(" in listener.signature

            # Verify dependencies
            assert len(manifest.dependencies) == 1
            dependency = manifest.dependencies[0]
            assert isinstance(dependency, DependencyInfo)
            assert dependency.name == "other_plugin"
            assert dependency.version == ">=1.0.0"
            assert dependency.optional is False
            assert dependency.description == "Required dependency"

            # Verify config schema
            assert manifest.config_schema is not None
            assert manifest.config_schema["type"] == "object"

        finally:
            Path(temp_path).unlink()

    def test_yaml_content_to_pydantic(self) -> None:
        """Test conversion from YAML content string."""
        yaml_content = """
manifest_version: "1.0.0"
metadata:
  name: "simple_plugin"
  version: "1.0.0"
runtime:
  plugginger_version: ">=0.9.0"
"""

        converter = ManifestConverter()
        manifest = converter.yaml_content_to_pydantic(yaml_content, "test_source")

        assert isinstance(manifest, PluginManifest)
        assert manifest.metadata.name == "simple_plugin"
        assert manifest.metadata.version == "1.0.0"
        assert manifest.runtime.plugginger_version == ">=0.9.0"

    def test_invalid_yaml_error_handling(self) -> None:
        """Test error handling for invalid YAML."""
        invalid_yaml = """
metadata:
  name: "test_plugin"
  version: "1.0.0"
runtime:
  plugginger_version: ">=0.9.0"
  invalid_field: [unclosed list
"""

        converter = ManifestConverter()

        with pytest.raises(PluginRegistrationError) as exc_info:
            converter.yaml_content_to_pydantic(invalid_yaml, "test_source")

        assert "Invalid YAML" in str(exc_info.value)

    def test_missing_required_fields_error(self) -> None:
        """Test error handling for missing required fields."""
        yaml_content = """
manifest_version: "1.0.0"
metadata:
  name: "test_plugin"
  # Missing version field
runtime:
  # Missing plugginger_version field
"""

        converter = ManifestConverter()

        with pytest.raises(PluginRegistrationError) as exc_info:
            converter.yaml_content_to_pydantic(yaml_content, "test_source")

        assert "Manifest conversion failed" in str(exc_info.value)

    def test_file_not_found_error(self) -> None:
        """Test error handling for missing files."""
        converter = ManifestConverter()

        with pytest.raises(PluginRegistrationError) as exc_info:
            converter.yaml_to_pydantic("nonexistent_file.yaml")

        assert "Manifest file not found" in str(exc_info.value)

    def test_empty_sections_handling(self) -> None:
        """Test handling of empty or missing sections."""
        yaml_content = """
manifest_version: "1.0.0"
metadata:
  name: "minimal_plugin"
  version: "1.0.0"
runtime:
  plugginger_version: ">=0.9.0"
"""

        converter = ManifestConverter()
        manifest = converter.yaml_content_to_pydantic(yaml_content, "test_source")

        # Should have empty lists for missing sections
        assert len(manifest.services) == 0
        assert len(manifest.event_listeners) == 0
        assert len(manifest.dependencies) == 0
        assert manifest.config_schema is None

    def test_parameter_kind_inference(self) -> None:
        """Test parameter kind inference based on required/default flags."""
        yaml_content = """
manifest_version: "1.0.0"
metadata:
  name: "test_plugin"
  version: "1.0.0"
runtime:
  plugginger_version: ">=0.9.0"
services:
  - name: "test_method"
    parameters:
      - name: "required_param"
        type: "str"
        required: true
      - name: "optional_param"
        type: "int"
        required: false
        default: 100
      - name: "optional_no_default"
        type: "bool"
        required: false
"""

        converter = ManifestConverter()
        manifest = converter.yaml_content_to_pydantic(yaml_content, "test_source")

        service = manifest.services[0]
        params = service.parameters

        # Required parameter should be POSITIONAL_OR_KEYWORD
        assert params[0].kind == ParameterKind.POSITIONAL_OR_KEYWORD
        assert params[0].default is None

        # Optional with default should be KEYWORD_ONLY
        assert params[1].kind == ParameterKind.KEYWORD_ONLY
        assert params[1].default == 100

        # Optional without default should be KEYWORD_ONLY
        assert params[2].kind == ParameterKind.KEYWORD_ONLY
        assert params[2].default is None
