"""
YAML to Pydantic manifest converter.

This module provides the ManifestConverter class that converts user-friendly
YAML manifest formats to the framework's internal Pydantic schema format.

The converter handles:
- Service definitions with simplified parameter syntax
- Event listener definitions with pattern matching
- Dependency declarations with version constraints
- Configuration schemas with JSON Schema format
- Metadata transformation and validation

Key Features:
- Automatic method signature generation
- Parameter kind inference (positional, keyword, etc.)
- Type annotation parsing and validation
- Clear error messages with YAML line numbers
- Backward compatibility with existing manifests
"""

from __future__ import annotations

import logging
from pathlib import Path
from typing import Any

import yaml
from pydantic import ValidationError

from plugginger.core.exceptions import PluginRegistrationError
from plugginger.schemas.manifest import (
    DependencyInfo,
    EventListenerInfo,
    ParameterInfo,
    ParameterKind,
    PluginManifest,
    PluginMetadata,
    PluginRuntime,
    ServiceInfo,
)


class ManifestConverter:
    """
    Converts user-friendly YAML manifests to framework Pydantic format.

    This converter bridges the gap between the user-friendly YAML format
    used in reference applications and the detailed Pydantic schema format
    required by the framework's internal validation system.
    """

    def __init__(self, logger: logging.Logger | None = None) -> None:
        """
        Initialize the manifest converter.

        Args:
            logger: Optional logger for debugging and error reporting
        """
        self._logger = logger or logging.getLogger(__name__)

    def yaml_to_pydantic(self, yaml_path: str | Path) -> PluginManifest:
        """
        Convert user-friendly YAML manifest to Pydantic format.

        Args:
            yaml_path: Path to the YAML manifest file

        Returns:
            Validated PluginManifest in framework format

        Raises:
            PluginRegistrationError: If YAML is invalid or conversion fails
        """
        yaml_path = Path(yaml_path)

        try:
            # Load YAML content
            with open(yaml_path, encoding='utf-8') as f:
                yaml_content = f.read()

            yaml_data = yaml.safe_load(yaml_content)
            if not isinstance(yaml_data, dict):
                raise ValueError("YAML content must be a dictionary")

            self._logger.debug(f"Converting YAML manifest from {yaml_path}")

            # Convert to Pydantic format
            return self._convert_yaml_data(yaml_data, yaml_path)

        except FileNotFoundError:
            raise PluginRegistrationError(f"Manifest file not found: {yaml_path}") from None
        except yaml.YAMLError as e:
            raise PluginRegistrationError(f"Invalid YAML in {yaml_path}: {e}") from e
        except Exception as e:
            raise PluginRegistrationError(f"Manifest conversion failed for {yaml_path}: {e}") from e

    def yaml_content_to_pydantic(self, yaml_content: str, source_name: str = "<string>") -> PluginManifest:
        """
        Convert YAML content string to Pydantic format.

        Args:
            yaml_content: YAML content as string
            source_name: Name for error messages (e.g., file path)

        Returns:
            Validated PluginManifest in framework format

        Raises:
            PluginRegistrationError: If YAML is invalid or conversion fails
        """
        try:
            yaml_data = yaml.safe_load(yaml_content)
            if not isinstance(yaml_data, dict):
                raise ValueError("YAML content must be a dictionary")

            self._logger.debug(f"Converting YAML content from {source_name}")

            return self._convert_yaml_data(yaml_data, source_name)

        except yaml.YAMLError as e:
            raise PluginRegistrationError(f"Invalid YAML in {source_name}: {e}") from e
        except Exception as e:
            raise PluginRegistrationError(f"Manifest conversion failed for {source_name}: {e}") from e

    def _convert_yaml_data(self, yaml_data: dict[str, Any], source_name: str | Path) -> PluginManifest:
        """
        Convert parsed YAML data to Pydantic format.

        Args:
            yaml_data: Parsed YAML data as dictionary
            source_name: Source name for error messages

        Returns:
            Validated PluginManifest in framework format

        Raises:
            PluginRegistrationError: If conversion fails
        """
        try:
            # Convert metadata
            metadata = self._convert_metadata(yaml_data.get("metadata", {}))

            # Convert runtime
            runtime = self._convert_runtime(yaml_data.get("runtime", {}))

            # Convert services
            services = self._convert_services(yaml_data.get("services", []))

            # Convert event listeners
            event_listeners = self._convert_event_listeners(yaml_data.get("event_listeners", []))

            # Convert dependencies
            dependencies = self._convert_dependencies(yaml_data.get("dependencies", []))

            # Create manifest
            manifest = PluginManifest(
                manifest_version=yaml_data.get("manifest_version", "1.0.0"),
                metadata=metadata,
                runtime=runtime,
                services=services,
                event_listeners=event_listeners,
                dependencies=dependencies,
                config_schema=yaml_data.get("config_schema"),
            )

            self._logger.info(f"Successfully converted manifest from {source_name}")
            return manifest

        except ValidationError as e:
            # Provide detailed validation error with field paths
            error_details = []
            for error in e.errors():
                field_path = ".".join(str(loc) for loc in error["loc"])
                error_details.append(f"{field_path}: {error['msg']}")

            error_msg = f"Manifest validation failed for {source_name}:\n" + "\n".join(error_details)
            raise PluginRegistrationError(error_msg) from e
        except Exception as e:
            raise PluginRegistrationError(f"Manifest conversion failed for {source_name}: {e}") from e

    def _convert_metadata(self, metadata_data: dict[str, Any]) -> PluginMetadata:
        """Convert metadata section from YAML to Pydantic format."""
        # Handle tags -> keywords conversion
        keywords = metadata_data.get("tags", metadata_data.get("keywords", []))

        return PluginMetadata(
            name=metadata_data["name"],
            version=metadata_data["version"],
            description=metadata_data.get("description"),
            author=metadata_data.get("author"),
            homepage=metadata_data.get("homepage"),
            repository=metadata_data.get("repository"),
            license=metadata_data.get("license"),
            keywords=keywords,
        )

    def _convert_runtime(self, runtime_data: dict[str, Any]) -> PluginRuntime:
        """Convert runtime section from YAML to Pydantic format."""
        return PluginRuntime(
            execution_mode=runtime_data.get("execution_mode", "thread"),
            python_version=runtime_data.get("python_version"),
            plugginger_version=runtime_data["plugginger_version"],
        )

    def _convert_services(self, services_data: list[dict[str, Any]]) -> list[ServiceInfo]:
        """Convert services section from YAML to Pydantic format."""
        services = []

        for service_data in services_data:
            # Extract service information
            service_name = service_data["name"]
            method_name = service_name  # Use service name as method name
            description = service_data.get("description")

            # Convert parameters
            parameters = self._convert_parameters(service_data.get("parameters", []))

            # Generate method signature
            signature = self._generate_method_signature(method_name, parameters, service_data.get("returns"))

            # Extract return annotation
            return_annotation = self._extract_return_annotation(service_data.get("returns"))

            services.append(ServiceInfo(
                name=service_name,
                method_name=method_name,
                description=description,
                signature=signature,
                parameters=parameters,
                return_annotation=return_annotation,
            ))

        return services

    def _convert_parameters(self, params_data: list[dict[str, Any]]) -> list[ParameterInfo]:
        """Convert parameter definitions from YAML to Pydantic format."""
        parameters = []

        for param_data in params_data:
            # Determine parameter kind based on required flag
            required = param_data.get("required", True)
            has_default = "default" in param_data

            if required and not has_default:
                kind = ParameterKind.POSITIONAL_OR_KEYWORD
            else:
                kind = ParameterKind.KEYWORD_ONLY

            # Extract default value
            default_value = param_data.get("default")

            parameters.append(ParameterInfo(
                name=param_data["name"],
                kind=kind,
                annotation=param_data.get("type", "Any"),
                default=default_value,
            ))

        return parameters

    def _generate_method_signature(
        self,
        method_name: str,
        parameters: list[ParameterInfo],
        returns_data: dict[str, Any] | None
    ) -> str:
        """Generate method signature string from parameters and return type."""
        # Build parameter strings
        param_strings = []
        for param in parameters:
            param_str = param.name
            if param.annotation:
                param_str += f": {param.annotation}"
            if param.default is not None:
                if isinstance(param.default, str):
                    param_str += f' = "{param.default}"'
                else:
                    param_str += f" = {param.default}"
            param_strings.append(param_str)

        # Build signature
        params_str = ", ".join(param_strings)
        signature = f"def {method_name}({params_str})"

        # Add return annotation
        if returns_data and "type" in returns_data:
            signature += f" -> {returns_data['type']}"
        else:
            signature += " -> None"

        return signature

    def _extract_return_annotation(self, returns_data: dict[str, Any] | None) -> str | None:
        """Extract return type annotation from returns data."""
        if returns_data and "type" in returns_data:
            return_type = returns_data["type"]
            if isinstance(return_type, str):
                return return_type
        return None

    def _convert_event_listeners(self, listeners_data: list[dict[str, Any]]) -> list[EventListenerInfo]:
        """Convert event listeners section from YAML to Pydantic format."""
        listeners = []

        for listener_data in listeners_data:
            # Extract event pattern(s)
            event_pattern = listener_data.get("event_pattern")
            patterns = [event_pattern] if event_pattern else []

            # Extract handler method name
            method_name = listener_data["handler"]

            # Generate signature for event listener
            signature = f"def {method_name}(self, event_data: dict[str, Any]) -> None"

            # Create parameter info for event_data
            parameters = [
                ParameterInfo(
                    name="event_data",
                    kind=ParameterKind.POSITIONAL_OR_KEYWORD,
                    annotation="dict[str, Any]",
                )
            ]

            listeners.append(EventListenerInfo(
                patterns=patterns,
                method_name=method_name,
                description=listener_data.get("description"),
                priority=listener_data.get("priority", 0),
                signature=signature,
                parameters=parameters,
            ))

        return listeners

    def _convert_dependencies(self, deps_data: list[dict[str, Any]]) -> list[DependencyInfo]:
        """Convert dependencies section from YAML to Pydantic format."""
        dependencies = []

        for dep_data in deps_data:
            dependencies.append(DependencyInfo(
                name=dep_data["name"],
                version=dep_data.get("version"),
                optional=dep_data.get("optional", False),
                description=dep_data.get("description"),
            ))

        return dependencies
