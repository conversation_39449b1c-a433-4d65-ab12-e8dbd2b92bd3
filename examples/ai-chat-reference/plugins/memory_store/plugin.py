"""Memory Store Plugin - In-Memory Storage for Chat History and Session Management."""

from __future__ import annotations

import uuid
from datetime import datetime, timezone
from typing import Any

from pydantic import BaseModel

from plugginger.api.app import PluggingerAppInstance
from plugginger.api.events import on_event
from plugginger.api.plugin import PluginBase, plugin
from plugginger.api.service import service


@plugin(name="memory_store", version="1.0.0")
class MemoryStorePlugin(PluginBase):
    """
    In-Memory Storage Plugin for Chat History and Session Management.
    
    Provides persistent storage for chat conversations, messages, and session data.
    Uses in-memory storage for simplicity but can be extended to Redis/Database.
    """

    def __init__(self, app: PluggingerAppInstance, **injected_dependencies: Any) -> None:
        """Initialize the memory store plugin."""
        super().__init__(app, **injected_dependencies)
        
        # In-memory storage structures
        self._conversations: dict[str, dict[str, Any]] = {}
        self._messages: dict[str, list[dict[str, Any]]] = {}
        
    async def setup(self, plugin_config: BaseModel) -> None:
        """Setup the memory store plugin."""
        self.app.logger.info("MemoryStore plugin initialized with in-memory storage")

    @service()
    async def create_conversation(self, title: str = "New Conversation") -> str:
        """
        Create a new conversation and return its ID.
        
        Args:
            title: Optional title for the conversation
            
        Returns:
            The unique conversation ID
        """
        conversation_id = str(uuid.uuid4())
        
        self._conversations[conversation_id] = {
            "id": conversation_id,
            "title": title,
            "created_at": datetime.now(timezone.utc).isoformat(),
            "updated_at": datetime.now(timezone.utc).isoformat(),
            "message_count": 0
        }
        
        self._messages[conversation_id] = []
        
        # Emit event
        await self.app.emit_event("memory.conversation_created", {
            "conversation_id": conversation_id,
            "title": title,
            "created_at": self._conversations[conversation_id]["created_at"]
        })
        
        self.app.logger.debug(f"Created conversation {conversation_id} with title '{title}'")
        return conversation_id

    @service()
    async def store_message(
        self,
        conversation_id: str,
        role: str,
        content: str,
        metadata: dict[str, Any] | None = None
    ) -> None:
        """
        Store a message in the conversation history.
        
        Args:
            conversation_id: The conversation to store the message in
            role: Message role ("user", "assistant", "system")
            content: The message content
            metadata: Optional metadata for the message
        """
        if conversation_id not in self._conversations:
            raise ValueError(f"Conversation {conversation_id} not found")
            
        if role not in ["user", "assistant", "system"]:
            raise ValueError(f"Invalid role '{role}'. Must be 'user', 'assistant', or 'system'")
        
        message = {
            "id": str(uuid.uuid4()),
            "role": role,
            "content": content,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "metadata": metadata or {}
        }
        
        self._messages[conversation_id].append(message)
        
        # Update conversation metadata
        self._conversations[conversation_id]["updated_at"] = message["timestamp"]
        self._conversations[conversation_id]["message_count"] += 1
        
        # Emit event
        await self.app.emit_event("memory.message_stored", {
            "conversation_id": conversation_id,
            "message_id": message["id"],
            "role": role,
            "content": content,
            "timestamp": message["timestamp"]
        })
        
        self.app.logger.debug(f"Stored {role} message in conversation {conversation_id}")

    @service()
    async def get_conversation_history(
        self,
        conversation_id: str,
        limit: int = 50,
        include_metadata: bool = False
    ) -> list[dict[str, Any]]:
        """
        Get conversation history with messages.
        
        Args:
            conversation_id: The conversation ID to retrieve
            limit: Maximum number of messages to return (most recent first)
            include_metadata: Whether to include message metadata
            
        Returns:
            List of messages in chronological order
        """
        if conversation_id not in self._conversations:
            raise ValueError(f"Conversation {conversation_id} not found")
        
        messages = self._messages[conversation_id]
        
        # Get most recent messages up to limit
        recent_messages = messages[-limit:] if limit > 0 else messages
        
        # Format messages for response
        formatted_messages = []
        for msg in recent_messages:
            formatted_msg = {
                "id": msg["id"],
                "role": msg["role"],
                "content": msg["content"],
                "timestamp": msg["timestamp"]
            }
            if include_metadata:
                formatted_msg["metadata"] = msg["metadata"]
            formatted_messages.append(formatted_msg)
        
        return formatted_messages

    @service()
    async def list_conversations(self) -> list[dict[str, Any]]:
        """
        List all conversations with basic metadata.
        
        Returns:
            List of conversation summaries
        """
        conversations = []
        for conv_id, conv_data in self._conversations.items():
            conversations.append({
                "id": conv_id,
                "title": conv_data["title"],
                "created_at": conv_data["created_at"],
                "updated_at": conv_data["updated_at"],
                "message_count": conv_data["message_count"]
            })
        
        # Sort by most recently updated
        conversations.sort(key=lambda x: x["updated_at"], reverse=True)
        return conversations

    @service()
    async def get_conversation_info(self, conversation_id: str) -> dict[str, Any]:
        """
        Get detailed information about a specific conversation.
        
        Args:
            conversation_id: The conversation ID
            
        Returns:
            Conversation metadata and statistics
        """
        if conversation_id not in self._conversations:
            raise ValueError(f"Conversation {conversation_id} not found")
        
        return self._conversations[conversation_id].copy()

    @service()
    async def delete_conversation(self, conversation_id: str) -> bool:
        """
        Delete a conversation and all its messages.
        
        Args:
            conversation_id: The conversation ID to delete
            
        Returns:
            True if deleted, False if not found
        """
        if conversation_id not in self._conversations:
            return False
        
        del self._conversations[conversation_id]
        del self._messages[conversation_id]
        
        # Emit event
        await self.app.emit_event("memory.conversation_deleted", {
            "conversation_id": conversation_id,
            "deleted_at": datetime.now(timezone.utc).isoformat()
        })
        
        self.app.logger.info(f"Deleted conversation {conversation_id}")
        return True

    @service()
    async def get_storage_stats(self) -> dict[str, Any]:
        """
        Get storage statistics for monitoring.
        
        Returns:
            Storage statistics and metrics
        """
        total_messages = sum(len(messages) for messages in self._messages.values())
        
        return {
            "total_conversations": len(self._conversations),
            "total_messages": total_messages,
            "storage_type": "in_memory",
            "plugin_version": "1.0.0"
        }

    @on_event("chat.response_generated")
    async def handle_chat_response(self, event_data: dict[str, Any]) -> None:
        """
        Handle chat response events to automatically store AI responses.
        
        Args:
            event_data: Event data containing response information
        """
        conversation_id = event_data.get("conversation_id")
        response_content = event_data.get("response")
        
        if conversation_id and response_content:
            await self.store_message(
                conversation_id=conversation_id,
                role="assistant",
                content=response_content,
                metadata={
                    "model": event_data.get("model"),
                    "tokens_used": event_data.get("tokens_used"),
                    "response_time": event_data.get("response_time")
                }
            )
