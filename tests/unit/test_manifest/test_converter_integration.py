"""
Integration tests for ManifestConverter with real Reference App manifests.

Tests the converter against actual YAML manifests from the AI Chat Reference App
to ensure compatibility and proper conversion.
"""

from __future__ import annotations

from pathlib import Path

import pytest

from plugginger.manifest.converter import ManifestConverter
from plugginger.schemas.manifest import PluginManifest


class TestManifestConverterIntegration:
    """Integration test cases for ManifestConverter with real manifests."""

    def test_memory_store_manifest_conversion(self) -> None:
        """Test conversion of memory_store plugin manifest."""
        manifest_path = Path("examples/ai-chat-reference/plugins/memory_store/manifest.yaml")

        if not manifest_path.exists():
            pytest.skip(f"Reference app manifest not found: {manifest_path}")

        converter = ManifestConverter()
        manifest = converter.yaml_to_pydantic(manifest_path)

        # Verify basic structure
        assert isinstance(manifest, PluginManifest)
        assert manifest.metadata.name == "memory_store"
        assert manifest.metadata.version == "1.0.0"
        assert "storage" in manifest.metadata.keywords

        # Verify services were converted
        assert len(manifest.services) == 7  # Expected number of services
        service_names = [s.name for s in manifest.services]
        assert "create_conversation" in service_names
        assert "store_message" in service_names
        assert "get_conversation_history" in service_names

        # Verify event listeners
        assert len(manifest.event_listeners) == 1
        listener = manifest.event_listeners[0]
        assert listener.patterns == ["chat.response_generated"]
        assert listener.method_name == "handle_chat_response"

        # Verify dependencies (should be empty for memory_store)
        assert len(manifest.dependencies) == 0

    def test_chat_ai_manifest_conversion(self) -> None:
        """Test conversion of chat_ai plugin manifest."""
        manifest_path = Path("examples/ai-chat-reference/plugins/chat_ai/manifest.yaml")

        if not manifest_path.exists():
            pytest.skip(f"Reference app manifest not found: {manifest_path}")

        converter = ManifestConverter()
        manifest = converter.yaml_to_pydantic(manifest_path)

        # Verify basic structure
        assert isinstance(manifest, PluginManifest)
        assert manifest.metadata.name == "chat_ai"
        assert manifest.metadata.version == "1.0.0"
        assert "ai" in manifest.metadata.keywords

        # Verify services
        assert len(manifest.services) == 3  # Expected number of services
        service_names = [s.name for s in manifest.services]
        assert "generate_response" in service_names
        assert "get_model_info" in service_names
        assert "set_default_model" in service_names

        # Verify dependencies
        assert len(manifest.dependencies) == 1
        dependency = manifest.dependencies[0]
        assert dependency.name == "memory_store"
        assert dependency.optional is False

    def test_web_api_manifest_conversion(self) -> None:
        """Test conversion of web_api plugin manifest."""
        manifest_path = Path("examples/ai-chat-reference/plugins/web_api/manifest.yaml")

        if not manifest_path.exists():
            pytest.skip(f"Reference app manifest not found: {manifest_path}")

        converter = ManifestConverter()
        manifest = converter.yaml_to_pydantic(manifest_path)

        # Verify basic structure
        assert isinstance(manifest, PluginManifest)
        assert manifest.metadata.name == "web_api"
        assert manifest.metadata.version == "1.0.0"

    def test_all_reference_manifests_valid(self) -> None:
        """Test that all reference app manifests can be converted without errors."""
        reference_plugins_dir = Path("examples/ai-chat-reference/plugins")

        if not reference_plugins_dir.exists():
            pytest.skip("Reference app plugins directory not found")

        converter = ManifestConverter()
        converted_manifests = []

        # Find all manifest.yaml files
        for manifest_path in reference_plugins_dir.rglob("manifest.yaml"):
            try:
                manifest = converter.yaml_to_pydantic(manifest_path)
                converted_manifests.append((manifest_path, manifest))
            except Exception as e:
                pytest.fail(f"Failed to convert manifest {manifest_path}: {e}")

        # Should have found and converted at least 3 manifests
        assert len(converted_manifests) >= 3

        # All converted manifests should be valid
        for _manifest_path, manifest in converted_manifests:
            assert isinstance(manifest, PluginManifest)
            assert manifest.metadata.name
            assert manifest.metadata.version
            assert manifest.runtime.plugginger_version

    def test_service_parameter_conversion_accuracy(self) -> None:
        """Test that service parameters are converted accurately."""
        manifest_path = Path("examples/ai-chat-reference/plugins/memory_store/manifest.yaml")

        if not manifest_path.exists():
            pytest.skip(f"Reference app manifest not found: {manifest_path}")

        converter = ManifestConverter()
        manifest = converter.yaml_to_pydantic(manifest_path)

        # Find the create_conversation service
        create_conv_service = None
        for service in manifest.services:
            if service.name == "create_conversation":
                create_conv_service = service
                break

        assert create_conv_service is not None

        # Verify parameter conversion
        assert len(create_conv_service.parameters) == 1
        title_param = create_conv_service.parameters[0]
        assert title_param.name == "title"
        assert title_param.annotation == "str"
        assert title_param.default == "New Conversation"

        # Verify signature generation
        assert "def create_conversation(" in create_conv_service.signature
        assert "title: str = \"New Conversation\"" in create_conv_service.signature
        assert "-> str" in create_conv_service.signature

    def test_event_listener_conversion_accuracy(self) -> None:
        """Test that event listeners are converted accurately."""
        manifest_path = Path("examples/ai-chat-reference/plugins/chat_ai/manifest.yaml")

        if not manifest_path.exists():
            pytest.skip(f"Reference app manifest not found: {manifest_path}")

        converter = ManifestConverter()
        manifest = converter.yaml_to_pydantic(manifest_path)

        # Verify event listener conversion
        assert len(manifest.event_listeners) == 1
        listener = manifest.event_listeners[0]
        assert listener.patterns == ["memory.conversation_created"]
        assert listener.method_name == "handle_new_conversation"
        assert listener.priority == 5

        # Verify signature generation
        assert "def handle_new_conversation(" in listener.signature
        assert "event_data: dict[str, Any]" in listener.signature
        assert "-> None" in listener.signature
