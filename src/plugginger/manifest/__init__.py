"""
Manifest conversion and compatibility utilities.

This package provides utilities for converting between user-friendly YAML
manifest formats and the framework's internal Pydantic schema format.

The conversion system enables:
- User-friendly YAML manifests (as used in Reference App)
- Framework-compatible Pydantic validation
- AI-agent readable structured metadata
- Backward compatibility with existing apps

Example usage:
    ```python
    from plugginger.manifest import ManifestConverter

    # Convert YAML to Pydantic format
    converter = ManifestConverter()
    manifest = converter.yaml_to_pydantic("manifest.yaml")

    # Use with framework
    builder.include_plugin_with_manifest(MyPlugin, manifest)
    ```
"""

from plugginger.manifest.converter import ManifestConverter

__all__ = ["ManifestConverter"]
