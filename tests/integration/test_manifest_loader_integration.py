"""
Integration tests for ManifestLoader with ManifestConverter.

Tests the integration between ManifestLoader and ManifestConverter to ensure
that user-friendly YAML manifests are automatically converted and loaded
by the framework.
"""

from __future__ import annotations

import logging
from pathlib import Path

import pytest

from plugginger._internal.validation.manifest_loader import Manifest<PERSON>oader
from plugginger.core.exceptions import PluginRegistrationError
from plugginger.schemas.manifest import PluginManifest


class DummyPlugin:
    """Dummy plugin class for testing."""
    pass


class TestManifestLoaderIntegration:
    """Integration test cases for ManifestLoader with converter."""

    def test_load_user_friendly_yaml_manifest(self) -> None:
        """Test loading user-friendly YAML manifest via ManifestLoader."""
        # Use the memory_store manifest from reference app
        manifest_path = Path("examples/ai-chat-reference/plugins/memory_store/manifest.yaml")

        if not manifest_path.exists():
            pytest.skip(f"Reference app manifest not found: {manifest_path}")

        # Create logger to capture debug messages
        logger = logging.getLogger("test_manifest_loader")
        logger.setLevel(logging.DEBUG)

        loader = ManifestLoader(logger)

        # Load manifest using explicit path
        manifest = loader.load_plugin_manifest(
            DummyPlugin,
            manifest_path=manifest_path,
            require_manifest=True
        )

        # Verify successful conversion
        assert manifest is not None
        assert isinstance(manifest, PluginManifest)
        assert manifest.metadata.name == "memory_store"
        assert manifest.metadata.version == "1.0.0"

        # Verify services were converted properly
        assert len(manifest.services) == 7
        service_names = [s.name for s in manifest.services]
        assert "create_conversation" in service_names
        assert "store_message" in service_names

        # Verify event listeners
        assert len(manifest.event_listeners) == 1
        assert manifest.event_listeners[0].patterns == ["chat.response_generated"]

    def test_load_chat_ai_manifest_with_dependencies(self) -> None:
        """Test loading chat_ai manifest with dependencies."""
        manifest_path = Path("examples/ai-chat-reference/plugins/chat_ai/manifest.yaml")

        if not manifest_path.exists():
            pytest.skip(f"Reference app manifest not found: {manifest_path}")

        loader = ManifestLoader()
        manifest = loader.load_plugin_manifest(
            DummyPlugin,
            manifest_path=manifest_path,
            require_manifest=True
        )

        # Verify dependency conversion
        assert manifest is not None
        assert len(manifest.dependencies) == 1
        dependency = manifest.dependencies[0]
        assert dependency.name == "memory_store"
        assert dependency.optional is False

    def test_fallback_to_standard_format(self) -> None:
        """Test that loader falls back to standard Pydantic format when needed."""
        # Create a standard Pydantic format manifest
        standard_yaml = """
manifest_version: "1.0.0"
metadata:
  name: "standard_plugin"
  version: "1.0.0"
  keywords: []
runtime:
  execution_mode: "thread"
  plugginger_version: ">=0.9.0"
services: []
event_listeners: []
dependencies: []
generated_at: "2025-06-02T12:00:00Z"
generated_by: "plugginger"
"""

        import tempfile
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            f.write(standard_yaml)
            temp_path = f.name

        try:
            loader = ManifestLoader()
            manifest = loader.load_plugin_manifest(
                DummyPlugin,
                manifest_path=temp_path,
                require_manifest=True
            )

            assert manifest is not None
            assert manifest.metadata.name == "standard_plugin"

        finally:
            Path(temp_path).unlink()

    def test_error_handling_for_invalid_manifests(self) -> None:
        """Test error handling for completely invalid manifests."""
        invalid_yaml = """
this_is_not: "a valid manifest"
missing_required_fields: true
"""

        import tempfile
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            f.write(invalid_yaml)
            temp_path = f.name

        try:
            loader = ManifestLoader()

            with pytest.raises(PluginRegistrationError):
                loader.load_plugin_manifest(
                    DummyPlugin,
                    manifest_path=temp_path,
                    require_manifest=True
                )

        finally:
            Path(temp_path).unlink()

    def test_optional_manifest_handling(self) -> None:
        """Test handling when manifest is optional and not found."""
        loader = ManifestLoader()

        # Should return None when manifest is not required and not found
        manifest = loader.load_plugin_manifest(
            DummyPlugin,
            manifest_path="nonexistent_manifest.yaml",
            require_manifest=False
        )

        assert manifest is None

    def test_required_manifest_error(self) -> None:
        """Test error when required manifest is not found."""
        loader = ManifestLoader()

        with pytest.raises(PluginRegistrationError) as exc_info:
            loader.load_plugin_manifest(
                DummyPlugin,
                manifest_path="nonexistent_manifest.yaml",
                require_manifest=True
            )

        assert "Required manifest file not found" in str(exc_info.value)

    def test_all_reference_manifests_loadable(self) -> None:
        """Test that all reference app manifests can be loaded via ManifestLoader."""
        reference_plugins_dir = Path("examples/ai-chat-reference/plugins")

        if not reference_plugins_dir.exists():
            pytest.skip("Reference app plugins directory not found")

        loader = ManifestLoader()
        loaded_manifests = []

        # Find all manifest.yaml files
        for manifest_path in reference_plugins_dir.rglob("manifest.yaml"):
            try:
                manifest = loader.load_plugin_manifest(
                    DummyPlugin,
                    manifest_path=manifest_path,
                    require_manifest=True
                )
                loaded_manifests.append((manifest_path, manifest))
            except Exception as e:
                pytest.fail(f"Failed to load manifest {manifest_path}: {e}")

        # Should have loaded at least 3 manifests
        assert len(loaded_manifests) >= 3

        # All loaded manifests should be valid
        for _manifest_path, manifest in loaded_manifests:
            assert isinstance(manifest, PluginManifest)
            assert manifest.metadata.name
            assert manifest.metadata.version
