"""Integration tests for AI-Chat Reference App."""

from __future__ import annotations

import asyncio

import pytest

from app import create_app


class TestAIChatIntegration:
    """Integration tests for the complete AI-Chat application."""

    @pytest.mark.asyncio
    async def test_app_creation_and_startup(self) -> None:
        """Test that the app can be created and started successfully."""
        # Create app
        app = create_app(enable_manifests=False)
        
        # Start all plugins
        await app.start_all_plugins()
        
        try:
            # Verify all services are available
            services = app.list_services()
            
            # Check memory store services
            assert "memory_store.create_conversation" in services
            assert "memory_store.store_message" in services
            assert "memory_store.get_conversation_history" in services
            
            # Check chat AI services
            assert "chat_ai.generate_response" in services
            assert "chat_ai.get_model_info" in services
            
            # Check web API services
            assert "web_api.get_health_status" in services
            assert "web_api.start_server" in services
            
        finally:
            await app.stop_all_plugins()

    @pytest.mark.asyncio
    async def test_complete_chat_flow(self) -> None:
        """Test the complete chat flow from message to response."""
        app = create_app(enable_manifests=False)
        await app.start_all_plugins()
        
        try:
            # Create a conversation
            conversation_id = await app.call_service("memory_store.create_conversation", "Test Chat")
            assert conversation_id
            
            # Generate AI response
            response = await app.call_service(
                "chat_ai.generate_response",
                message="Hello, how are you?",
                conversation_id=conversation_id
            )
            assert response
            assert isinstance(response, str)

            # Wait for async event processing to complete
            await asyncio.sleep(0.1)

            # Check conversation history
            history = await app.call_service(
                "memory_store.get_conversation_history",
                conversation_id=conversation_id
            )
            
            # Should have user message and AI response
            assert len(history) >= 2
            
            # Check message roles
            user_messages = [msg for msg in history if msg["role"] == "user"]
            ai_messages = [msg for msg in history if msg["role"] == "assistant"]
            
            assert len(user_messages) >= 1
            assert len(ai_messages) >= 1
            assert user_messages[0]["content"] == "Hello, how are you?"
            
        finally:
            await app.stop_all_plugins()

    @pytest.mark.asyncio
    async def test_health_check(self) -> None:
        """Test the health check functionality."""
        app = create_app(enable_manifests=False)
        await app.start_all_plugins()
        
        try:
            # Get health status
            health = await app.call_service("web_api.get_health_status")
            
            assert health["status"] == "healthy"
            assert "timestamp" in health
            assert "version" in health
            assert health["memory_store"] == "available"
            assert health["chat_ai"] == "available"
            
        finally:
            await app.stop_all_plugins()

    @pytest.mark.asyncio
    async def test_plugin_dependencies(self) -> None:
        """Test that plugin dependencies are correctly resolved."""
        app = create_app(enable_manifests=False)
        await app.start_all_plugins()
        
        try:
            # Test that chat_ai can access memory_store
            model_info = await app.call_service("chat_ai.get_model_info")
            assert "mock_mode" in model_info
            
            # Test that web_api can access both dependencies
            server_info = await app.call_service("web_api.get_server_info")
            assert "plugin_version" in server_info
            
            # Test storage stats
            storage_stats = await app.call_service("memory_store.get_storage_stats")
            assert storage_stats["storage_type"] == "in_memory"
            assert "total_conversations" in storage_stats
            
        finally:
            await app.stop_all_plugins()

    @pytest.mark.asyncio
    async def test_event_system(self) -> None:
        """Test that events are properly emitted and handled."""
        app = create_app(enable_manifests=False)
        await app.start_all_plugins()
        
        try:
            # Create conversation (should emit event)
            conversation_id = await app.call_service("memory_store.create_conversation", "Event Test")
            
            # Generate response (should emit multiple events)
            await app.call_service(
                "chat_ai.generate_response",
                message="Test message",
                conversation_id=conversation_id
            )

            # Wait for async event processing to complete
            await asyncio.sleep(0.1)

            # Check that messages were stored (indicating events were handled)
            history = await app.call_service(
                "memory_store.get_conversation_history",
                conversation_id=conversation_id
            )
            
            # Should have both user and assistant messages
            assert len(history) >= 2
            
        finally:
            await app.stop_all_plugins()

    @pytest.mark.asyncio
    async def test_multiple_conversations(self) -> None:
        """Test handling multiple conversations simultaneously."""
        app = create_app(enable_manifests=False)
        await app.start_all_plugins()
        
        try:
            # Create multiple conversations
            conv1 = await app.call_service("memory_store.create_conversation", "Chat 1")
            conv2 = await app.call_service("memory_store.create_conversation", "Chat 2")
            
            # Send messages to both
            await app.call_service("chat_ai.generate_response", "Hello from chat 1", conv1)
            await app.call_service("chat_ai.generate_response", "Hello from chat 2", conv2)

            # Wait for async event processing to complete
            await asyncio.sleep(0.1)

            # Check both conversations
            history1 = await app.call_service("memory_store.get_conversation_history", conv1)
            history2 = await app.call_service("memory_store.get_conversation_history", conv2)
            
            assert len(history1) >= 2
            assert len(history2) >= 2
            
            # Check that messages are in correct conversations
            assert any("chat 1" in msg["content"] for msg in history1)
            assert any("chat 2" in msg["content"] for msg in history2)
            
            # List all conversations
            conversations = await app.call_service("memory_store.list_conversations")
            assert len(conversations) >= 2
            
        finally:
            await app.stop_all_plugins()

    @pytest.mark.asyncio
    async def test_error_handling(self) -> None:
        """Test error handling in various scenarios."""
        app = create_app(enable_manifests=False)
        await app.start_all_plugins()
        
        try:
            # Test invalid conversation ID - Framework wraps in ServiceExecutionError
            from plugginger.core.exceptions import ServiceExecutionError
            with pytest.raises(ServiceExecutionError):
                await app.call_service(
                    "memory_store.get_conversation_history",
                    "invalid_conversation_id"
                )
            
            # Test invalid message role - Framework wraps in ServiceExecutionError
            conversation_id = await app.call_service("memory_store.create_conversation")
            with pytest.raises(ServiceExecutionError):
                await app.call_service(
                    "memory_store.store_message",
                    conversation_id=conversation_id,
                    role="invalid_role",
                    content="test"
                )
            
        finally:
            await app.stop_all_plugins()
