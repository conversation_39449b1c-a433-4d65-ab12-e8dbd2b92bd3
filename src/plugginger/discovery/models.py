"""
Data models for plugin discovery system.

This module defines the data structures used by the plugin discovery system
to represent discovered plugins, their metadata, and discovery results.
"""

from __future__ import annotations

from dataclasses import dataclass
from pathlib import Path
from typing import Any

from plugginger.schemas.manifest import PluginManifest


@dataclass
class DiscoveredPlugin:
    """
    Represents a plugin discovered during the discovery process.

    This class contains all the information needed to include a discovered
    plugin in an application, including its manifest, module path, and
    any discovery-specific metadata.
    """

    name: str
    """The plugin name from the manifest."""

    plugin_class: type[Any]
    """The actual plugin class to be included."""

    manifest: PluginManifest
    """The loaded and validated plugin manifest."""

    manifest_path: Path
    """Path to the manifest file."""

    module_path: Path
    """Path to the plugin module/directory."""

    discovery_source: str
    """Source where this plugin was discovered (e.g., directory path)."""

    def __str__(self) -> str:
        """String representation of the discovered plugin."""
        return f"DiscoveredPlugin(name='{self.name}', class={self.plugin_class.__name__})"

    def __repr__(self) -> str:
        """Detailed representation of the discovered plugin."""
        return (
            f"DiscoveredPlugin("
            f"name='{self.name}', "
            f"class={self.plugin_class.__name__}, "
            f"manifest_path='{self.manifest_path}', "
            f"module_path='{self.module_path}', "
            f"source='{self.discovery_source}'"
            f")"
        )


@dataclass
class DiscoveryResult:
    """
    Result of a plugin discovery operation.

    Contains the list of successfully discovered plugins and any errors
    encountered during the discovery process.
    """

    discovered_plugins: list[DiscoveredPlugin]
    """List of successfully discovered plugins."""

    errors: list[str]
    """List of error messages encountered during discovery."""

    discovery_source: str
    """Source where discovery was performed."""

    total_manifests_found: int
    """Total number of manifest files found."""

    successful_discoveries: int
    """Number of plugins successfully discovered and loaded."""

    def __post_init__(self) -> None:
        """Validate the discovery result after initialization."""
        if self.successful_discoveries != len(self.discovered_plugins):
            self.successful_discoveries = len(self.discovered_plugins)

    @property
    def success_rate(self) -> float:
        """Calculate the success rate of the discovery operation."""
        if self.total_manifests_found == 0:
            return 1.0  # No manifests found is considered 100% success
        return self.successful_discoveries / self.total_manifests_found

    @property
    def has_errors(self) -> bool:
        """Check if any errors occurred during discovery."""
        return len(self.errors) > 0

    @property
    def plugin_names(self) -> list[str]:
        """Get list of discovered plugin names."""
        return [plugin.name for plugin in self.discovered_plugins]

    def __str__(self) -> str:
        """String representation of the discovery result."""
        return (
            f"DiscoveryResult("
            f"plugins={self.successful_discoveries}, "
            f"errors={len(self.errors)}, "
            f"success_rate={self.success_rate:.1%}"
            f")"
        )

    def __repr__(self) -> str:
        """Detailed representation of the discovery result."""
        return (
            f"DiscoveryResult("
            f"discovered_plugins={len(self.discovered_plugins)}, "
            f"errors={len(self.errors)}, "
            f"discovery_source='{self.discovery_source}', "
            f"total_manifests_found={self.total_manifests_found}, "
            f"successful_discoveries={self.successful_discoveries}, "
            f"success_rate={self.success_rate:.1%}"
            f")"
        )
