#!/bin/bash
# Plugginger Repository Rollback Script
# Implements Multi-Level Rollback Strategy from ROADMAP.md
# Usage: ./scripts/rollback.sh [level] [reason]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

analyze_damage() {
    log_info "Analyzing repository damage..."
    
    # Check if we're in the right directory
    if [[ ! -f "pyproject.toml" ]] || [[ ! -d "src/plugginger" ]]; then
        log_error "Not in Plugginger repository root!"
        exit 1
    fi
    
    local failed_tests=0
    local mypy_errors=0
    local ruff_errors=0
    
    # Count test failures
    if pytest --tb=no -q > /tmp/pytest_output 2>&1; then
        log_success "All tests passing"
    else
        failed_tests=$(grep -c "FAILED" /tmp/pytest_output || echo "0")
        log_warning "Tests failing: $failed_tests"
    fi
    
    # Count mypy errors
    if mypy --strict src/ > /tmp/mypy_output 2>&1; then
        log_success "No mypy errors"
    else
        mypy_errors=$(grep -c "error:" /tmp/mypy_output || echo "0")
        log_warning "MyPy errors: $mypy_errors"
    fi
    
    # Count ruff errors
    if ruff check > /tmp/ruff_output 2>&1; then
        log_success "No ruff errors"
    else
        ruff_errors=$(grep -c "error" /tmp/ruff_output || echo "0")
        log_warning "Ruff errors: $ruff_errors"
    fi
    
    # Recommend rollback level
    if [[ $failed_tests -eq 0 && $mypy_errors -eq 0 && $ruff_errors -eq 0 ]]; then
        echo "0"  # No rollback needed
    elif [[ $failed_tests -le 5 && $mypy_errors -le 5 && $ruff_errors -le 5 ]]; then
        echo "1"  # Level 1: backup/current
    elif [[ $failed_tests -le 20 && $mypy_errors -le 20 && $ruff_errors -le 20 ]]; then
        echo "2"  # Level 2: backup/current + analysis
    elif [[ $failed_tests -le 100 ]]; then
        echo "3"  # Level 3: backup/sprint-N-start
    else
        echo "4"  # Level 4: backup/milestone
    fi
}

list_available_backups() {
    log_info "Available backup branches:"
    
    echo -e "\n${GREEN}Current Backups:${NC}"
    git branch -r | grep "backup/current" | sed 's/origin\//  /' || echo "  None"
    
    echo -e "\n${BLUE}Sprint Backups:${NC}"
    git branch -r | grep "backup/sprint" | sed 's/origin\//  /' | sort -r | head -5 || echo "  None"
    
    echo -e "\n${YELLOW}Milestone Backups:${NC}"
    git branch -r | grep "backup/milestone" | sed 's/origin\//  /' | sort -r | head -5 || echo "  None"
    
    echo -e "\n${RED}Emergency Backups:${NC}"
    git branch -r | grep "backup/emergency" | sed 's/origin\//  /' | sort -r | head -5 || echo "  None"
}

perform_rollback() {
    local level=$1
    local reason=$2
    local backup_branch=""
    
    case $level in
        "1")
            backup_branch="backup/current"
            log_warning "Level 1 Rollback: Minor issues - rolling back to backup/current"
            ;;
        "2")
            backup_branch="backup/current"
            log_warning "Level 2 Rollback: Build errors - rolling back to backup/current"
            ;;
        "3")
            # Find latest sprint backup
            backup_branch=$(git branch -r | grep "backup/sprint" | sed 's/origin\///' | sort -r | head -1)
            if [[ -z "$backup_branch" ]]; then
                log_error "No sprint backup found! Falling back to milestone backup."
                backup_branch=$(git branch -r | grep "backup/milestone" | sed 's/origin\///' | sort -r | head -1)
            fi
            log_error "Level 3 Rollback: Framework damage - rolling back to $backup_branch"
            ;;
        "4")
            # Find latest milestone backup
            backup_branch=$(git branch -r | grep "backup/milestone" | sed 's/origin\///' | sort -r | head -1)
            if [[ -z "$backup_branch" ]]; then
                log_error "No milestone backup found! Manual intervention required."
                exit 1
            fi
            log_error "Level 4 Rollback: Critical damage - rolling back to $backup_branch"
            ;;
        *)
            log_error "Invalid rollback level: $level"
            exit 1
            ;;
    esac
    
    if [[ -z "$backup_branch" ]]; then
        log_error "No suitable backup branch found for level $level rollback!"
        exit 1
    fi
    
    # Confirm rollback
    echo -e "\n${RED}WARNING: This will reset your repository to $backup_branch${NC}"
    echo -e "${RED}All uncommitted changes will be lost!${NC}"
    echo -e "Reason: $reason"
    echo ""
    read -p "Are you sure you want to proceed? Type 'YES' to confirm: " -r
    
    if [[ "$REPLY" != "YES" ]]; then
        log_info "Rollback cancelled."
        exit 0
    fi
    
    # Create emergency backup of current state
    local emergency_backup="backup/emergency-before-rollback-$(date +%Y-%m-%d-%H-%M)"
    log_info "Creating emergency backup of current state: $emergency_backup"
    git checkout -b $emergency_backup
    git push origin $emergency_backup
    
    # Perform rollback
    log_info "Performing rollback to $backup_branch..."
    git checkout main
    git reset --hard origin/$backup_branch
    git push origin main --force-with-lease
    
    # Document rollback
    local rollback_message="rollback(level-$level): $reason

Rolled back from $(git rev-parse $emergency_backup) to $(git rev-parse HEAD)
Emergency backup created: $emergency_backup
Rollback performed: $(date)
"
    
    git commit --allow-empty -m "$rollback_message"
    git push origin main
    
    log_success "Rollback completed successfully!"
    log_info "Emergency backup of previous state: $emergency_backup"
    log_warning "Please analyze the cause and implement preventive measures."
}

show_usage() {
    echo "Plugginger Rollback Script"
    echo "Usage: $0 [LEVEL|auto] [REASON]"
    echo ""
    echo "Rollback Levels:"
    echo "  auto        - Automatically detect damage level and recommend rollback"
    echo "  1           - Level 1: Minor test failures (1-5 tests)"
    echo "  2           - Level 2: Build errors (mypy/ruff issues)"
    echo "  3           - Level 3: Framework damage (>20 test failures)"
    echo "  4           - Level 4: Critical damage (repository unusable)"
    echo "  list        - List available backup branches"
    echo ""
    echo "Examples:"
    echo "  $0 auto \"KI damaged repository\""
    echo "  $0 1 \"Single test failure after refactoring\""
    echo "  $0 3 \"Major framework changes broke everything\""
    echo "  $0 list"
}

# Main script
if [[ $# -eq 0 ]]; then
    show_usage
    exit 1
fi

LEVEL=$1
REASON=${2:-"Manual rollback"}

case $LEVEL in
    "auto")
        damage_level=$(analyze_damage)
        if [[ $damage_level -eq 0 ]]; then
            log_success "No damage detected. Repository appears healthy."
            exit 0
        else
            log_warning "Damage level $damage_level detected. Recommending rollback."
            perform_rollback $damage_level "$REASON"
        fi
        ;;
    "list")
        list_available_backups
        ;;
    "1"|"2"|"3"|"4")
        perform_rollback $LEVEL "$REASON"
        ;;
    *)
        log_error "Unknown option: $LEVEL"
        show_usage
        exit 1
        ;;
esac
